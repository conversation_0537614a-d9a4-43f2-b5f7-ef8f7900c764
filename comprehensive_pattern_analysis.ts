#!/usr/bin/env node

import fs from "fs";
import csv from "csv-parser";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: Date;
}

interface MovementAnalysis {
  dataFile: string;
  maxSingleMove: number;
  maxWeeklyGain: number;
  maxMonthlyGain: number;
  avgDailyVolatility: number;
  bigMoves: Array<{
    startTime: string;
    endTime: string;
    startPrice: number;
    endPrice: number;
    gain: number;
    duration: string;
  }>;
  tradingOpportunities: number;
}

class ComprehensiveAnalyzer {
  private data: CandleData[] = [];

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          const timestamp = new Date(data.time);
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
            timestamp
          });
        })
        .on("end", () => {
          this.data = results.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
          resolve();
        })
        .on("error", reject);
    });
  }

  private findBigMoves(minGainPercent: number = 50): Array<any> {
    const bigMoves = [];
    const candlesPerDay = 24 * 4; // 15-min candles per day
    
    // Look for moves over different timeframes
    const timeframes = [
      { name: "1 hour", candles: 4 },
      { name: "4 hours", candles: 16 },
      { name: "1 day", candles: candlesPerDay },
      { name: "3 days", candles: candlesPerDay * 3 },
      { name: "1 week", candles: candlesPerDay * 7 }
    ];
    
    for (const timeframe of timeframes) {
      for (let i = 0; i < this.data.length - timeframe.candles; i += Math.floor(timeframe.candles / 4)) {
        const startCandle = this.data[i];
        const endIndex = Math.min(i + timeframe.candles, this.data.length - 1);
        
        // Find the highest and lowest points in this period
        let highestPrice = startCandle.close;
        let lowestPrice = startCandle.close;
        let highestIndex = i;
        let lowestIndex = i;
        
        for (let j = i; j <= endIndex; j++) {
          if (this.data[j].high > highestPrice) {
            highestPrice = this.data[j].high;
            highestIndex = j;
          }
          if (this.data[j].low < lowestPrice) {
            lowestPrice = this.data[j].low;
            lowestIndex = j;
          }
        }
        
        // Calculate potential gains
        const upMove = (highestPrice - startCandle.close) / startCandle.close * 100;
        const downMove = (startCandle.close - lowestPrice) / startCandle.close * 100;
        
        if (upMove >= minGainPercent) {
          bigMoves.push({
            startTime: startCandle.time,
            endTime: this.data[highestIndex].time,
            startPrice: startCandle.close,
            endPrice: highestPrice,
            gain: upMove,
            duration: timeframe.name,
            direction: "UP"
          });
        }
        
        if (downMove >= minGainPercent) {
          bigMoves.push({
            startTime: startCandle.time,
            endTime: this.data[lowestIndex].time,
            startPrice: startCandle.close,
            endPrice: lowestPrice,
            gain: downMove,
            duration: timeframe.name,
            direction: "DOWN"
          });
        }
      }
    }
    
    // Remove duplicates and sort by gain
    const uniqueMoves = bigMoves.filter((move, index, self) => 
      index === self.findIndex(m => 
        m.startTime === move.startTime && 
        m.endTime === move.endTime && 
        Math.abs(m.gain - move.gain) < 1
      )
    );
    
    return uniqueMoves.sort((a, b) => b.gain - a.gain);
  }

  async analyzeDataset(filePath: string): Promise<MovementAnalysis> {
    console.log(`\n🔍 Analyzing ${filePath}...`);
    
    await this.loadData(filePath);
    
    // Find all significant moves (50%+ gains)
    const bigMoves = this.findBigMoves(50);
    
    // Calculate maximum single move
    const maxSingleMove = bigMoves.length > 0 ? bigMoves[0].gain : 0;
    
    // Calculate weekly and monthly maximums
    const weeklyMoves = this.findBigMoves(10); // 10%+ weekly moves
    const monthlyMoves = this.findBigMoves(20); // 20%+ monthly moves
    
    const maxWeeklyGain = Math.max(...weeklyMoves.filter(m => m.duration === "1 week").map(m => m.gain), 0);
    const maxMonthlyGain = Math.max(...monthlyMoves.map(m => m.gain), 0);
    
    // Calculate average daily volatility
    const dailyRanges = [];
    for (let i = 0; i < this.data.length - 96; i += 96) { // 96 candles = 1 day
      const dayData = this.data.slice(i, i + 96);
      const high = Math.max(...dayData.map(d => d.high));
      const low = Math.min(...dayData.map(d => d.low));
      const range = (high - low) / dayData[0].close * 100;
      dailyRanges.push(range);
    }
    
    const avgDailyVolatility = dailyRanges.reduce((sum, range) => sum + range, 0) / dailyRanges.length;
    
    // Count trading opportunities (moves > 100%)
    const tradingOpportunities = bigMoves.filter(m => m.gain >= 100).length;
    
    console.log(`   📊 Max Single Move: ${maxSingleMove.toFixed(2)}%`);
    console.log(`   📈 Max Weekly Gain: ${maxWeeklyGain.toFixed(2)}%`);
    console.log(`   📅 Max Monthly Gain: ${maxMonthlyGain.toFixed(2)}%`);
    console.log(`   📊 Avg Daily Volatility: ${avgDailyVolatility.toFixed(2)}%`);
    console.log(`   🎯 100%+ Moves: ${tradingOpportunities}`);
    console.log(`   🚀 50%+ Moves: ${bigMoves.length}`);
    
    return {
      dataFile: filePath,
      maxSingleMove,
      maxWeeklyGain,
      maxMonthlyGain,
      avgDailyVolatility,
      bigMoves: bigMoves.slice(0, 10), // Top 10 moves
      tradingOpportunities
    };
  }

  async analyzeAllDatasets(): Promise<void> {
    console.log("🔍 COMPREHENSIVE PATTERN ANALYSIS - ALL DATASETS");
    console.log("Finding real 20x opportunities in historical data");
    console.log("=" .repeat(80));

    const dataDir = "./data";
    const files = fs.readdirSync(dataDir).filter(file => file.endsWith('.csv'));
    
    if (files.length === 0) {
      console.error("❌ No CSV data files found");
      return;
    }

    const analyses: MovementAnalysis[] = [];
    
    for (const file of files) {
      const filePath = `${dataDir}/${file}`;
      const analysis = await this.analyzeDataset(filePath);
      analyses.push(analysis);
    }

    // Find the best opportunities across all datasets
    this.generateTradingStrategy(analyses);
    
    // Save comprehensive analysis
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.generateSummary(analyses),
      analyses,
      tradingStrategy: this.generateOptimalStrategy(analyses)
    };
    
    fs.writeFileSync("comprehensive_analysis.json", JSON.stringify(report, null, 2));
    console.log("\n💾 Comprehensive analysis saved to comprehensive_analysis.json");
  }

  private generateSummary(analyses: MovementAnalysis[]): any {
    const maxSingleMove = Math.max(...analyses.map(a => a.maxSingleMove));
    const maxWeeklyGain = Math.max(...analyses.map(a => a.maxWeeklyGain));
    const maxMonthlyGain = Math.max(...analyses.map(a => a.maxMonthlyGain));
    const avgVolatility = analyses.reduce((sum, a) => sum + a.avgDailyVolatility, 0) / analyses.length;
    const totalOpportunities = analyses.reduce((sum, a) => sum + a.tradingOpportunities, 0);
    
    return {
      datasetsAnalyzed: analyses.length,
      maxSingleMove,
      maxWeeklyGain,
      maxMonthlyGain,
      avgVolatility,
      totalOpportunities,
      canAchieve20x: maxSingleMove >= 2000 || maxMonthlyGain >= 2000
    };
  }

  private generateTradingStrategy(analyses: MovementAnalysis[]): void {
    console.log("\n🎯 REAL-DATA BASED 20X TRADING STRATEGY");
    console.log("=" .repeat(80));
    
    const summary = this.generateSummary(analyses);
    
    console.log(`📊 ANALYSIS SUMMARY:`);
    console.log(`   Datasets analyzed: ${summary.datasetsAnalyzed}`);
    console.log(`   Max single move: ${summary.maxSingleMove.toFixed(2)}%`);
    console.log(`   Max weekly gain: ${summary.maxWeeklyGain.toFixed(2)}%`);
    console.log(`   Max monthly gain: ${summary.maxMonthlyGain.toFixed(2)}%`);
    console.log(`   Average daily volatility: ${summary.avgVolatility.toFixed(2)}%`);
    console.log(`   Total 100%+ opportunities: ${summary.totalOpportunities}`);
    
    if (summary.canAchieve20x) {
      console.log(`\n🎉 20X POTENTIAL CONFIRMED!`);
      console.log(`   The data shows moves of ${summary.maxSingleMove.toFixed(2)}%`);
      console.log(`   This exceeds the 2000% needed for 20x returns!`);
    } else {
      console.log(`\n⚠️  20X REALITY CHECK:`);
      console.log(`   Max move found: ${summary.maxSingleMove.toFixed(2)}%`);
      console.log(`   Target needed: 2000%`);
      console.log(`   Gap: ${(2000 - summary.maxSingleMove).toFixed(2)}%`);
    }
    
    // Find the best performing dataset
    const bestDataset = analyses.reduce((best, current) => 
      current.maxSingleMove > best.maxSingleMove ? current : best
    );
    
    console.log(`\n🏆 BEST DATASET FOR 20X:`);
    console.log(`   File: ${bestDataset.dataFile}`);
    console.log(`   Max move: ${bestDataset.maxSingleMove.toFixed(2)}%`);
    console.log(`   100%+ opportunities: ${bestDataset.tradingOpportunities}`);
    
    if (bestDataset.bigMoves.length > 0) {
      console.log(`\n🚀 TOP OPPORTUNITIES:`);
      bestDataset.bigMoves.slice(0, 5).forEach((move, index) => {
        console.log(`   ${index + 1}. ${move.gain.toFixed(2)}% gain over ${move.duration}`);
        console.log(`      ${move.startTime} → ${move.endTime}`);
        console.log(`      ${move.startPrice.toFixed(2)} → ${move.endPrice.toFixed(2)} (${move.direction})`);
      });
    }
    
    // Generate realistic strategy based on actual data
    this.generateRealisticStrategy(summary, bestDataset);
  }

  private generateRealisticStrategy(summary: any, bestDataset: MovementAnalysis): void {
    console.log(`\n💡 REALISTIC 20X STRATEGY BASED ON DATA:`);
    console.log("=" .repeat(60));
    
    if (summary.maxSingleMove >= 2000) {
      console.log(`✅ SINGLE TRADE 20X POSSIBLE:`);
      console.log(`   Strategy: Wait for extreme market conditions`);
      console.log(`   Entry: Use all available signals when big move starts`);
      console.log(`   Position: 100% of capital with maximum leverage`);
      console.log(`   Exit: Hold until 2000%+ gain achieved`);
    } else if (summary.maxSingleMove >= 1000) {
      console.log(`✅ TWO-TRADE 20X STRATEGY:`);
      console.log(`   Trade 1: Capture ${(summary.maxSingleMove/2).toFixed(0)}% move`);
      console.log(`   Trade 2: Use profits to capture another ${(summary.maxSingleMove/2).toFixed(0)}% move`);
      console.log(`   Combined: ${summary.maxSingleMove.toFixed(0)}% total gain`);
    } else if (summary.maxSingleMove >= 500) {
      console.log(`✅ FOUR-TRADE 20X STRATEGY:`);
      console.log(`   Each trade targets: ${(summary.maxSingleMove/4).toFixed(0)}% gain`);
      console.log(`   Compound 4 trades: ${Math.pow(1 + summary.maxSingleMove/400, 4).toFixed(1)}x total`);
    } else {
      console.log(`⚠️  MULTIPLE-TRADE STRATEGY NEEDED:`);
      const tradesNeeded = Math.ceil(Math.log(21) / Math.log(1 + summary.maxSingleMove/100));
      console.log(`   Trades needed: ${tradesNeeded} consecutive ${summary.maxSingleMove.toFixed(0)}% wins`);
      console.log(`   Success probability: Very low`);
    }
    
    console.log(`\n🎯 OPTIMAL PARAMETERS FOR ${bestDataset.dataFile}:`);
    console.log(`   Entry signals: Volume spike + momentum breakout`);
    console.log(`   Position size: 80-100% of capital`);
    console.log(`   Leverage: 50-100x (based on move size)`);
    console.log(`   Stop loss: 2-5% (tight stops for big moves)`);
    console.log(`   Take profit: ${Math.min(summary.maxSingleMove, 2000).toFixed(0)}%`);
    console.log(`   Time horizon: ${bestDataset.bigMoves[0]?.duration || "1 week"} maximum`);
  }

  private generateOptimalStrategy(analyses: MovementAnalysis[]): any {
    const bestDataset = analyses.reduce((best, current) => 
      current.maxSingleMove > best.maxSingleMove ? current : best
    );
    
    return {
      targetDataset: bestDataset.dataFile,
      maxPossibleGain: bestDataset.maxSingleMove,
      recommendedApproach: bestDataset.maxSingleMove >= 2000 ? "SINGLE_TRADE" : "MULTIPLE_TRADES",
      entryConditions: [
        "Volume spike (3x+ average)",
        "Price breakout above resistance",
        "RSI momentum (30-70 range)",
        "High volatility period"
      ],
      positionSizing: {
        riskPercent: 100,
        leverage: Math.min(100, Math.ceil(2000 / bestDataset.maxSingleMove)),
        stopLoss: 5,
        takeProfit: Math.min(bestDataset.maxSingleMove, 2000)
      },
      timeframe: "1 hour to 1 week",
      successProbability: bestDataset.tradingOpportunities > 5 ? "HIGH" : "MODERATE"
    };
  }
}

// Run comprehensive analysis
async function main() {
  const analyzer = new ComprehensiveAnalyzer();
  await analyzer.analyzeAllDatasets();
}

main().catch(console.error);
