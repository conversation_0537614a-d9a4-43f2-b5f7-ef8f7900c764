name: CI
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
jobs:
  code-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - uses: actions/cache@v2
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-modules-${{ hashFiles('**/yarn.lock') }}

      - name: Install dependencies
        run: yarn

      - name: Run checks
        run: yarn ci
