<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate 8X Trading Bot Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stat-card h3 {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 10px;
        }
        
        .stat-card .value {
            font-size: 1.8em;
            font-weight: bold;
        }
        
        .positive { color: #4CAF50; }
        .negative { color: #f44336; }
        .neutral { color: #FFC107; }
        
        .trades-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .trades-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .trades-container {
            max-height: 600px;
            overflow-y: auto;
        }
        
        .trade-log {
            background: rgba(0, 0, 0, 0.2);
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid;
        }
        
        .trade-log.OPEN { border-left-color: #4CAF50; }
        .trade-log.CLOSE { border-left-color: #f44336; }
        .trade-log.INFO { border-left-color: #2196F3; }
        .trade-log.WARNING { border-left-color: #FFC107; }
        .trade-log.ERROR { border-left-color: #f44336; }
        
        .trade-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .trade-type {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        
        .trade-type.OPEN { background: #4CAF50; }
        .trade-type.CLOSE { background: #f44336; }
        .trade-type.INFO { background: #2196F3; }
        .trade-type.WARNING { background: #FFC107; color: black; }
        .trade-type.ERROR { background: #f44336; }
        
        .trade-time {
            font-size: 0.9em;
            opacity: 0.7;
        }
        
        .trade-details {
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .status-online {
            background: #4CAF50;
            color: white;
        }
        
        .status-offline {
            background: #f44336;
            color: white;
        }
        
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .trades-header {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="status-indicator" id="status">🔴 Offline</div>
    
    <div class="container">
        <div class="header">
            <h1>🚀 Ultimate 8X Trading Bot</h1>
            <p>Real-time trading dashboard with live trade monitoring</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <h3>Current Balance</h3>
                <div class="value" id="balance">$0.00</div>
            </div>
            <div class="stat-card">
                <h3>Total PnL</h3>
                <div class="value" id="totalPnl">$0.00</div>
            </div>
            <div class="stat-card">
                <h3>Active Position</h3>
                <div class="value" id="position">None</div>
            </div>
            <div class="stat-card">
                <h3>Total Trades</h3>
                <div class="value" id="totalTrades">0</div>
            </div>
            <div class="stat-card">
                <h3>Win Rate</h3>
                <div class="value" id="winRate">0%</div>
            </div>
            <div class="stat-card">
                <h3>Market State</h3>
                <div class="value" id="marketState">Unknown</div>
            </div>
        </div>
        
        <div class="trades-section">
            <div class="trades-header">
                <h2>📊 Trade Logs</h2>
                <button class="refresh-btn" onclick="loadTrades()">🔄 Refresh</button>
            </div>
            <div class="trades-container" id="tradesContainer">
                <p style="text-align: center; opacity: 0.7;">Loading trades...</p>
            </div>
        </div>
    </div>

    <script>
        let lastUpdateTime = 0;
        
        async function loadTrades() {
            try {
                const response = await fetch('/api/trades');
                const trades = await response.json();
                
                displayTrades(trades);
                updateStats(trades);
                updateStatus(true);
                
            } catch (error) {
                console.error('Error loading trades:', error);
                updateStatus(false);
            }
        }
        
        function displayTrades(trades) {
            const container = document.getElementById('tradesContainer');
            
            if (trades.length === 0) {
                container.innerHTML = '<p style="text-align: center; opacity: 0.7;">No trades yet...</p>';
                return;
            }
            
            const tradesHtml = trades.reverse().map(trade => {
                const time = new Date(trade.timestamp).toLocaleString();
                let details = trade.message;
                
                if (trade.type === 'OPEN' || trade.type === 'CLOSE') {
                    details += `<br><strong>Symbol:</strong> ${trade.symbol}`;
                    if (trade.side) details += ` | <strong>Side:</strong> ${trade.side}`;
                    if (trade.price) details += ` | <strong>Price:</strong> $${trade.price.toFixed(2)}`;
                    if (trade.quantity) details += ` | <strong>Qty:</strong> ${trade.quantity}`;
                    if (trade.pnl !== undefined) details += ` | <strong>PnL:</strong> $${trade.pnl.toFixed(2)} (${trade.pnlPercent?.toFixed(2)}%)`;
                    if (trade.balance) details += ` | <strong>Balance:</strong> $${trade.balance.toFixed(2)}`;
                    if (trade.reason) details += ` | <strong>Reason:</strong> ${trade.reason}`;
                }
                
                return `
                    <div class="trade-log ${trade.type}">
                        <div class="trade-header">
                            <span class="trade-type ${trade.type}">${trade.type}</span>
                            <span class="trade-time">${time}</span>
                        </div>
                        <div class="trade-details">${details}</div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = tradesHtml;
        }
        
        function updateStats(trades) {
            let balance = 0;
            let totalPnl = 0;
            let totalTrades = 0;
            let winningTrades = 0;
            let currentPosition = 'None';
            let marketState = 'Unknown';
            
            trades.forEach(trade => {
                if (trade.balance) balance = trade.balance;
                if (trade.pnl) totalPnl += trade.pnl;
                if (trade.type === 'CLOSE') {
                    totalTrades++;
                    if (trade.pnl && trade.pnl > 0) winningTrades++;
                }
                if (trade.type === 'OPEN') {
                    currentPosition = `${trade.side} ${trade.symbol}`;
                }
                if (trade.marketState) marketState = trade.marketState;
            });
            
            const winRate = totalTrades > 0 ? (winningTrades / totalTrades * 100) : 0;
            
            document.getElementById('balance').textContent = `$${balance.toFixed(2)}`;
            document.getElementById('totalPnl').textContent = `$${totalPnl.toFixed(2)}`;
            document.getElementById('totalPnl').className = `value ${totalPnl >= 0 ? 'positive' : 'negative'}`;
            document.getElementById('position').textContent = currentPosition;
            document.getElementById('totalTrades').textContent = totalTrades;
            document.getElementById('winRate').textContent = `${winRate.toFixed(1)}%`;
            document.getElementById('winRate').className = `value ${winRate >= 50 ? 'positive' : 'negative'}`;
            document.getElementById('marketState').textContent = marketState;
        }
        
        function updateStatus(online) {
            const status = document.getElementById('status');
            if (online) {
                status.textContent = '🟢 Online';
                status.className = 'status-indicator status-online';
            } else {
                status.textContent = '🔴 Offline';
                status.className = 'status-indicator status-offline';
            }
        }
        
        // Auto-refresh every 5 seconds
        setInterval(loadTrades, 5000);
        
        // Initial load
        loadTrades();
    </script>
</body>
</html>
