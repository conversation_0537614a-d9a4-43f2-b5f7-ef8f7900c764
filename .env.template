# Testnet API Configuration (Get from https://testnet.binancefuture.com/)
TESTNET_API_KEY=your_testnet_api_key_here
TESTNET_API_SECRET=your_testnet_api_secret_here

# Mainnet API Configuration (Get from https://www.binance.com/en/my/settings/api-management)
MAINNET_API_KEY=your_mainnet_api_key_here
MAINNET_API_SECRET=your_mainnet_api_secret_here

# Network Configuration (testnet vs mainnet)
USE_TESTNET=true  # Start with testnet for safe testing
# Testnet URLs
TESTNET_HTTP_FUTURES=https://testnet.binancefuture.com
TESTNET_WS_FUTURES=wss://testnet.binancefuture.com/ws-fapi/v1
# Mainnet URLs (default Binance URLs)
MAINNET_HTTP_FUTURES=https://fapi.binance.com
MAINNET_WS_FUTURES=wss://fstream.binance.com/ws

# Trading Configuration
TRADING_SYMBOL=BTCUSDT
# INITIAL_BALANCE is automatically set from your Binance account balance
MAX_POSITION_SIZE=0.001
ENABLE_REAL_TRADING=false  # Set to true for live trading

# Web Dashboard Configuration
WEB_PORT=3000

# Risk Management
MAX_DAILY_LOSS=50
EMERGENCY_STOP_LOSS=80

# Setup Instructions:
# 1. Copy this file to .env: cp .env.template .env
# 2. Get testnet API keys from: https://testnet.binancefuture.com/
# 3. Update TESTNET_API_KEY and TESTNET_API_SECRET
# 4. Test with: npm run test-config
# 5. Start bot with: npm run bot
# 6. Access dashboard at: http://localhost:3000
#
# For mainnet trading:
# 1. Get real API keys from: https://www.binance.com/en/my/settings/api-management
# 2. Update MAINNET_API_KEY and MAINNET_API_SECRET
# 3. Set USE_TESTNET=false when ready for real trading
