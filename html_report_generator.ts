import fs from "fs";

interface ReportData {
  strategy8x?: any;
  strategy20x?: any;
  comparison?: any;
  timestamp: string;
  dataFile: string;
}

export class HTMLReportGenerator {
  private generateCSS(): string {
    return `
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }
      
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
      }
      
      .header {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }
      
      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }
      
      .header p {
        font-size: 1.2em;
        opacity: 0.9;
      }
      
      .content {
        padding: 30px;
      }
      
      .strategy-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
      }
      
      .strategy-card {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        border-left: 5px solid;
        transition: transform 0.3s ease;
      }
      
      .strategy-card:hover {
        transform: translateY(-5px);
      }
      
      .strategy-8x {
        border-left-color: #4CAF50;
      }
      
      .strategy-20x {
        border-left-color: #FF5722;
      }
      
      .strategy-title {
        font-size: 1.5em;
        font-weight: bold;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
      }
      
      .metric {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
      }
      
      .metric:last-child {
        border-bottom: none;
      }
      
      .metric-label {
        font-weight: 500;
        color: #666;
      }
      
      .metric-value {
        font-weight: bold;
        color: #333;
      }
      
      .positive {
        color: #4CAF50;
      }
      
      .negative {
        color: #f44336;
      }
      
      .comparison-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
      }
      
      .comparison-title {
        font-size: 1.8em;
        text-align: center;
        margin-bottom: 20px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
      }
      
      .winner-announcement {
        text-align: center;
        font-size: 1.3em;
        background: rgba(255,255,255,0.2);
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
      }
      
      .comparison-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        text-align: center;
      }
      
      .comparison-metric {
        background: rgba(255,255,255,0.1);
        padding: 15px;
        border-radius: 8px;
      }
      
      .trades-section {
        margin-top: 30px;
      }
      
      .trades-title {
        font-size: 1.5em;
        margin-bottom: 20px;
        color: #333;
        border-bottom: 2px solid #eee;
        padding-bottom: 10px;
      }
      
      .trades-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }
      
      .trade-list {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
      }
      
      .trade-item {
        background: white;
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 5px;
        border-left: 3px solid;
        font-size: 0.9em;
      }
      
      .trade-profit {
        border-left-color: #4CAF50;
      }
      
      .trade-loss {
        border-left-color: #f44336;
      }
      
      .footer {
        background: #333;
        color: white;
        text-align: center;
        padding: 20px;
        font-size: 0.9em;
      }
      
      .target-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: bold;
        margin-left: 10px;
      }
      
      .target-achieved {
        background: #4CAF50;
        color: white;
      }
      
      .target-missed {
        background: #f44336;
        color: white;
      }
      
      @media (max-width: 768px) {
        .strategy-grid,
        .comparison-grid,
        .trades-grid {
          grid-template-columns: 1fr;
        }
        
        .header h1 {
          font-size: 2em;
        }
        
        .content {
          padding: 20px;
        }
      }
    </style>
    `;
  }

  private formatNumber(num: number, decimals: number = 2): string {
    return num.toFixed(decimals);
  }

  private formatPercent(num: number): string {
    return `${num.toFixed(2)}%`;
  }

  private formatCurrency(num: number): string {
    return `${num.toFixed(2)} USDT`;
  }

  private generateStrategyCard(strategy: any, type: "8x" | "20x"): string {
    const targetAchieved = strategy.targetAchieved;
    const targetBadge = targetAchieved 
      ? `<span class="target-badge target-achieved">✅ TARGET ACHIEVED</span>`
      : `<span class="target-badge target-missed">❌ TARGET MISSED</span>`;

    const icon = type === "8x" ? "🎯" : "🚀";
    const title = type === "8x" ? "8X Strategy" : "20X Strategy";

    return `
    <div class="strategy-card strategy-${type}">
      <div class="strategy-title">
        ${icon} ${title}
        ${targetBadge}
      </div>
      
      <div class="metric">
        <span class="metric-label">Initial Balance:</span>
        <span class="metric-value">${this.formatCurrency(strategy.initialBalance)}</span>
      </div>
      
      <div class="metric">
        <span class="metric-label">Final Balance:</span>
        <span class="metric-value ${strategy.finalBalance > strategy.initialBalance ? 'positive' : 'negative'}">
          ${this.formatCurrency(strategy.finalBalance)}
        </span>
      </div>
      
      <div class="metric">
        <span class="metric-label">Total Return:</span>
        <span class="metric-value ${strategy.returnPercent > 0 ? 'positive' : 'negative'}">
          ${this.formatPercent(strategy.returnPercent)}
        </span>
      </div>
      
      <div class="metric">
        <span class="metric-label">Return Multiplier:</span>
        <span class="metric-value ${strategy.returnMultiplier > 1 ? 'positive' : 'negative'}">
          ${this.formatNumber(strategy.returnMultiplier)}x
        </span>
      </div>
      
      <div class="metric">
        <span class="metric-label">Total Trades:</span>
        <span class="metric-value">${strategy.totalTrades}</span>
      </div>
      
      <div class="metric">
        <span class="metric-label">Win Rate:</span>
        <span class="metric-value ${strategy.winRate > 50 ? 'positive' : 'negative'}">
          ${this.formatPercent(strategy.winRate)}
        </span>
      </div>
      
      <div class="metric">
        <span class="metric-label">Max Drawdown:</span>
        <span class="metric-value negative">${this.formatPercent(strategy.maxDrawdown)}</span>
      </div>
      
      <div class="metric">
        <span class="metric-label">Profit Factor:</span>
        <span class="metric-value ${strategy.profitFactor > 1 ? 'positive' : 'negative'}">
          ${this.formatNumber(strategy.profitFactor)}
        </span>
      </div>
    </div>
    `;
  }

  private generateTopTrades(trades: any[], title: string, count: number = 5): string {
    const topTrades = [...trades]
      .sort((a, b) => b.pnl - a.pnl)
      .slice(0, count);

    const tradeItems = topTrades.map(trade => {
      const tradeClass = trade.pnl > 0 ? 'trade-profit' : 'trade-loss';
      const pnlSign = trade.pnl > 0 ? '+' : '';
      return `
        <div class="trade-item ${tradeClass}">
          <strong>${trade.side}</strong> | 
          ${pnlSign}${this.formatCurrency(trade.pnl)} (${this.formatPercent(trade.pnlPercent * 100)}) |
          ${trade.entryReason}
        </div>
      `;
    }).join('');

    return `
    <div class="trade-list">
      <h4>${title}</h4>
      ${tradeItems}
    </div>
    `;
  }

  generateReport(data: ReportData): string {
    const { strategy8x, strategy20x, comparison, timestamp, dataFile } = data;

    const comparisonSection = comparison ? `
    <div class="comparison-section">
      <div class="comparison-title">🏆 Strategy Comparison Results</div>
      
      <div class="winner-announcement">
        🎉 Winner: <strong>${comparison.winner}</strong>
      </div>
      
      <div class="comparison-grid">
        <div class="comparison-metric">
          <h4>Performance</h4>
          <p>8X: ${this.formatNumber(comparison.performance8x)}x</p>
          <p>20X: ${this.formatNumber(comparison.performance20x)}x</p>
        </div>
        
        <div class="comparison-metric">
          <h4>Total Trades</h4>
          <p>8X: ${comparison.trades8x}</p>
          <p>20X: ${comparison.trades20x}</p>
        </div>
        
        <div class="comparison-metric">
          <h4>Win Rate</h4>
          <p>8X: ${this.formatPercent(comparison.winRate8x)}</p>
          <p>20X: ${this.formatPercent(comparison.winRate20x)}</p>
        </div>
      </div>
    </div>
    ` : '';

    const tradesSection = (strategy8x && strategy20x) ? `
    <div class="trades-section">
      <div class="trades-title">📊 Top Performing Trades</div>
      <div class="trades-grid">
        ${this.generateTopTrades(strategy8x.trades, "🎯 8X Strategy - Best Trades")}
        ${this.generateTopTrades(strategy20x.trades, "🚀 20X Strategy - Best Trades")}
      </div>
    </div>
    ` : '';

    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Ultimate Trading Strategy Report</title>
      ${this.generateCSS()}
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>🚀 Ultimate Trading Strategy Report</h1>
          <p>8X vs 20X Strategy Performance Analysis</p>
          <p style="font-size: 0.9em; margin-top: 10px;">
            Generated: ${new Date(timestamp).toLocaleString()} | Dataset: ${dataFile}
          </p>
        </div>
        
        <div class="content">
          ${comparisonSection}
          
          <div class="strategy-grid">
            ${strategy8x ? this.generateStrategyCard(strategy8x, "8x") : ''}
            ${strategy20x ? this.generateStrategyCard(strategy20x, "20x") : ''}
          </div>
          
          ${tradesSection}
        </div>
        
        <div class="footer">
          <p>🔥 Ultimate Trading Strategy System | Maximize Your Returns with AI-Powered Trading</p>
          <p>⚠️ Trading involves risk. Past performance does not guarantee future results.</p>
        </div>
      </div>
    </body>
    </html>
    `;
  }

  async generateFromResults(resultsFile: string, outputFile: string = "strategy_report.html"): Promise<void> {
    try {
      const rawData = fs.readFileSync(resultsFile, 'utf8');
      const data = JSON.parse(rawData);
      
      const reportData: ReportData = {
        strategy8x: data.strategy8x,
        strategy20x: data.strategy20x,
        comparison: data.comparison,
        timestamp: data.timestamp || new Date().toISOString(),
        dataFile: data.dataFile || "Unknown Dataset"
      };

      const html = this.generateReport(reportData);
      fs.writeFileSync(outputFile, html);
      
      console.log(`📄 HTML report generated: ${outputFile}`);
    } catch (error) {
      console.error("Error generating HTML report:", error);
    }
  }
}
