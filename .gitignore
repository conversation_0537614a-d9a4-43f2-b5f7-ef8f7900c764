# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Trading Bot Specific Files
# API Keys and sensitive configuration
config/
secrets/
keys/
*.key
*.pem
*.p12
*.pfx

# Trading logs and data
logs/
*.log
trading_logs/
execution_logs/

# Temporary trading data
temp/
tmp/
*.tmp

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Build outputs
build/
dist/
lib/

# Test outputs
test-results/
coverage/

# Local development files
.local/
local/

# Historical data cache (optional - uncomment if you want to ignore cached data)
# data/cache/
# *.csv.cache

# Compiled TypeScript
*.js.map
*.d.ts.map

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Package manager lock files (uncomment the ones you don't use)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Local environment overrides
.env.override
.env.*.local

# Trading strategy backups
strategies/backup/
*.strategy.bak

# Performance monitoring files
*.perf
performance/

# Memory dumps
*.heapsnapshot
*.cpuprofile

# PM2 process manager
.pm2/

# Docker
.dockerignore
docker-compose.override.yml

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Local SSL certificates
*.crt
*.cert
*.ca-bundle
