#!/usr/bin/env node

import csv from "csv-parser";
import fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  entryReason: string;
}

interface Trade {
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  entryTime: string;
  exitTime: string;
  size: number;
  pnl: number;
  pnlPercent: number;
  reason: string;
  entryReason: string;
  leverage: number;
  balanceBefore: number;
  balanceAfter: number;
}

class Smart20xStrategy {
  private data: CandleData[] = [];
  private balance: number = 40;
  private initialBalance: number = 40;
  private trades: Trade[] = [];

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateIndicators(index: number): any {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map((c) => c.close);
    const highs = candles.map((c) => c.high);
    const lows = candles.map((c) => c.low);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 8, values: closes });
    const emaSlowValues = EMA.calculate({ period: 21, values: closes });
    const smaValues = SMA.calculate({ period: 50, values: closes });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast:
        emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow:
        emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
    };
  }

  private resetStrategy(): void {
    this.balance = this.initialBalance;
    this.trades = [];
  }

  async testSmart20xStrategy(dataFile: string): Promise<any> {
    console.log(`\n🧠 Testing SMART 20X Strategy on: ${dataFile}`);
    console.log("=".repeat(60));

    this.resetStrategy();
    await this.loadData(dataFile);

    let position: Position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      entryReason: "",
    };
    let maxBalance = this.balance;
    let maxDrawdown = 0;
    let targetAchieved = false;
    let consecutiveLosses = 0;

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      // SMART ENTRY CONDITIONS - Quality over Quantity
      if (!position.side) {
        // Market analysis
        const volumeAvg =
          this.data
            .slice(Math.max(0, i - 20), i)
            .reduce((sum, c) => sum + c.volume, 0) / 20;
        const isHighVolume = candle.volume > volumeAvg * 1.5;
        const priceChange = Math.abs(
          (candle.close - candle.open) / candle.open
        );
        const isVolatile = priceChange > 0.008; // 0.8% price movement

        // Trend confirmation
        const isBullishTrend =
          indicators.emaFast > indicators.emaSlow &&
          candle.close > indicators.sma;
        const isBearishTrend =
          indicators.emaFast < indicators.emaSlow &&
          candle.close < indicators.sma;

        // Strong momentum signals
        const strongBullMomentum =
          candle.close > prevCandle.close * 1.005 &&
          candle.close > indicators.emaFast;
        const strongBearMomentum =
          candle.close < prevCandle.close * 0.995 &&
          candle.close < indicators.emaFast;

        // SMART LONG CONDITIONS - High probability setups
        const shouldEnterLong =
          (indicators.rsi < 35 && isBullishTrend && isHighVolume) ||
          (indicators.rsi < 40 && strongBullMomentum && isVolatile) ||
          (indicators.rsi < 30 &&
            candle.close > indicators.sma &&
            isHighVolume);

        // SMART SHORT CONDITIONS - High probability setups
        const shouldEnterShort =
          (indicators.rsi > 65 && isBearishTrend && isHighVolume) ||
          (indicators.rsi > 60 && strongBearMomentum && isVolatile) ||
          (indicators.rsi > 70 &&
            candle.close < indicators.sma &&
            isHighVolume);

        if (shouldEnterLong || shouldEnterShort) {
          // SMART POSITION SIZING - Adaptive based on performance
          const balanceMultiplier = this.balance / this.initialBalance;
          let riskPercent = 0.6; // Base 60% risk
          let leverage = 75; // Base 75x leverage

          // Reduce risk after consecutive losses
          if (consecutiveLosses >= 3) {
            riskPercent = 0.4;
            leverage = 50;
          } else if (consecutiveLosses >= 2) {
            riskPercent = 0.5;
            leverage = 60;
          }

          // Scale up carefully as we grow
          if (balanceMultiplier >= 10) {
            riskPercent = Math.min(0.8, riskPercent * 1.2);
            leverage = Math.min(100, leverage * 1.2);
          } else if (balanceMultiplier >= 5) {
            riskPercent = Math.min(0.75, riskPercent * 1.1);
            leverage = Math.min(90, leverage * 1.1);
          } else if (balanceMultiplier >= 2) {
            riskPercent = Math.min(0.7, riskPercent * 1.05);
            leverage = Math.min(85, leverage * 1.05);
          }

          const size = this.balance * riskPercent * leverage;
          const side = shouldEnterLong ? "LONG" : "SHORT";

          position = {
            side,
            size,
            entryPrice: candle.close,
            entryTime: candle.time,
            leverage,
            entryReason: "SMART_HIGH_PROBABILITY",
          };

          console.log(
            `🧠 SMART ${side} at ${
              candle.close
            } | RSI: ${indicators.rsi.toFixed(1)} | Risk: ${(
              riskPercent * 100
            ).toFixed(
              0
            )}% | Leverage: ${leverage}x | Balance: ${this.balance.toFixed(2)}`
          );
        }
      } else {
        // SMART EXIT CONDITIONS - Maximize winners, minimize losers
        const pnlPercent =
          position.side === "LONG"
            ? (candle.close - position.entryPrice) / position.entryPrice
            : (position.entryPrice - candle.close) / position.entryPrice;

        let shouldExit = false;
        let exitReason = "";

        // Dynamic targets based on balance growth and market conditions
        const balanceMultiplier = this.balance / this.initialBalance;
        let takeProfitTarget = 0.4; // Base 40% take profit
        let stopLossLimit = -0.08; // Base 8% stop loss

        // Adjust targets based on growth
        if (balanceMultiplier >= 15) {
          takeProfitTarget = 0.6; // 60% when 15x ahead
          stopLossLimit = -0.05; // 5% stop when ahead
        } else if (balanceMultiplier >= 10) {
          takeProfitTarget = 0.5; // 50% when 10x ahead
          stopLossLimit = -0.06; // 6% stop when ahead
        } else if (balanceMultiplier >= 5) {
          takeProfitTarget = 0.45; // 45% when 5x ahead
          stopLossLimit = -0.07; // 7% stop when ahead
        }

        // SMART TAKE PROFIT - Let winners run but secure profits
        if (pnlPercent >= takeProfitTarget) {
          shouldExit = true;
          exitReason = `SMART_PROFIT_${(takeProfitTarget * 100).toFixed(0)}%`;
        }
        // SMART STOP LOSS - Reasonable stops to avoid death by 1000 cuts
        else if (pnlPercent <= stopLossLimit) {
          shouldExit = true;
          exitReason = "SMART_STOP_LOSS";
        }
        // Partial profit at 20% to secure some gains
        else if (pnlPercent >= 0.2 && Math.random() < 0.25) {
          shouldExit = true;
          exitReason = "PARTIAL_PROFIT_20%";
        }
        // RSI reversal with confirmation
        else if (
          position.side === "LONG" &&
          indicators.rsi > 75 &&
          candle.close < indicators.emaFast
        ) {
          shouldExit = true;
          exitReason = "RSI_REVERSAL_CONFIRMED";
        } else if (
          position.side === "SHORT" &&
          indicators.rsi < 25 &&
          candle.close > indicators.emaFast
        ) {
          shouldExit = true;
          exitReason = "RSI_REVERSAL_CONFIRMED";
        }
        // Trend reversal based on EMA crossover
        else if (
          position.side === "LONG" &&
          indicators.emaFast < indicators.emaSlow &&
          pnlPercent > 0.1
        ) {
          shouldExit = true;
          exitReason = "TREND_REVERSAL";
        } else if (
          position.side === "SHORT" &&
          indicators.emaFast > indicators.emaSlow &&
          pnlPercent > 0.1
        ) {
          shouldExit = true;
          exitReason = "TREND_REVERSAL";
        }

        if (shouldExit) {
          const pnl =
            position.side === "LONG"
              ? ((candle.close - position.entryPrice) * position.size) /
                position.entryPrice
              : ((position.entryPrice - candle.close) * position.size) /
                position.entryPrice;

          const balanceBefore = this.balance;
          this.balance += pnl;

          // Track consecutive losses
          if (pnl < 0) {
            consecutiveLosses++;
          } else {
            consecutiveLosses = 0;
          }

          if (this.balance > maxBalance) maxBalance = this.balance;
          const currentDrawdown = (maxBalance - this.balance) / maxBalance;
          if (currentDrawdown > maxDrawdown) maxDrawdown = currentDrawdown;

          this.trades.push({
            side: position.side,
            entryPrice: position.entryPrice,
            exitPrice: candle.close,
            entryTime: position.entryTime,
            exitTime: candle.time,
            size: position.size,
            pnl,
            pnlPercent,
            reason: exitReason,
            entryReason: position.entryReason,
            leverage: position.leverage,
            balanceBefore,
            balanceAfter: this.balance,
          });

          const pnlColor = pnl > 0 ? "🟢" : "🔴";
          const currentMultiplier = this.balance / this.initialBalance;
          console.log(
            `${pnlColor} EXIT ${position.side} | PnL: ${pnl.toFixed(2)} (${(
              pnlPercent * 100
            ).toFixed(1)}%) | Balance: ${this.balance.toFixed(
              2
            )} (${currentMultiplier.toFixed(2)}x) | ${exitReason}`
          );

          position = {
            side: null,
            size: 0,
            entryPrice: 0,
            entryTime: "",
            leverage: 1,
            entryReason: "",
          };
        }
      }

      // Progress tracking
      const currentMultiplier = this.balance / this.initialBalance;

      if (currentMultiplier >= 5 && currentMultiplier < 5.1) {
        console.log(
          `🔥 5X MILESTONE! Balance: ${this.balance.toFixed(
            2
          )} USDT (${currentMultiplier.toFixed(2)}x) - 24% to target!`
        );
      } else if (currentMultiplier >= 10 && currentMultiplier < 10.1) {
        console.log(
          `🚀 10X MILESTONE! Balance: ${this.balance.toFixed(
            2
          )} USDT (${currentMultiplier.toFixed(2)}x) - 48% to target!`
        );
      } else if (currentMultiplier >= 15 && currentMultiplier < 15.1) {
        console.log(
          `💎 15X MILESTONE! Balance: ${this.balance.toFixed(
            2
          )} USDT (${currentMultiplier.toFixed(2)}x) - 71% to target!`
        );
      } else if (currentMultiplier >= 20 && currentMultiplier < 20.1) {
        console.log(
          `🌟 20X MILESTONE! Balance: ${this.balance.toFixed(
            2
          )} USDT (${currentMultiplier.toFixed(2)}x) - 95% to target!`
        );
      }

      // Check if 21x target achieved
      if (this.balance >= this.initialBalance * 21) {
        console.log(`🎉🎉🎉 21X TARGET ACHIEVED! 🎉🎉🎉`);
        console.log(
          `💰 Final Balance: ${this.balance.toFixed(
            2
          )} USDT (${currentMultiplier.toFixed(2)}x)`
        );
        console.log(
          `💎 Profit: ${(this.balance - this.initialBalance).toFixed(2)} USDT`
        );
        targetAchieved = true;
        break;
      }

      // Emergency exit if balance drops too low
      if (this.balance < this.initialBalance * 0.1) {
        console.log(
          `⚠️ EMERGENCY EXIT: Balance dropped to ${this.balance.toFixed(
            2
          )} USDT`
        );
        break;
      }
    }

    const winningTrades = this.trades.filter((t) => t.pnl > 0);
    const winRate =
      this.trades.length > 0
        ? (winningTrades.length / this.trades.length) * 100
        : 0;
    const returnMultiplier = this.balance / this.initialBalance;

    const result = {
      dataFile,
      targetAchieved,
      finalBalance: this.balance,
      returnMultiplier,
      totalTrades: this.trades.length,
      winRate,
      maxDrawdown: maxDrawdown * 100,
      trades: this.trades,
    };

    console.log(`\n📊 RESULT: ${targetAchieved ? "🎉 SUCCESS" : "❌ FAILED"}`);
    console.log(
      `   Final: ${this.balance.toFixed(2)} USDT (${returnMultiplier.toFixed(
        2
      )}x)`
    );
    console.log(
      `   Trades: ${this.trades.length} | Win Rate: ${winRate.toFixed(1)}%`
    );

    return result;
  }

  async testAllDatasets(): Promise<void> {
    console.log("🧠 SMART 20X STRATEGY - COMPREHENSIVE TESTING");
    console.log(
      "Intelligent approach to achieve 21x target with better risk management"
    );
    console.log("=".repeat(80));

    const dataDir = "./data";
    let dataFiles: string[] = [];

    try {
      const files = fs.readdirSync(dataDir);
      dataFiles = files
        .filter((file) => file.endsWith(".csv"))
        .map((file) => `${dataDir}/${file}`);
    } catch (error) {
      console.error("❌ Error loading data files:", error);
      return;
    }

    const results = [];
    let successCount = 0;

    for (const dataFile of dataFiles) {
      const result = await this.testSmart20xStrategy(dataFile);
      results.push(result);

      if (result.targetAchieved) {
        successCount++;
      }
    }

    // Summary
    console.log("\n" + "=".repeat(80));
    console.log("🏆 SMART 20X STRATEGY - FINAL SUMMARY");
    console.log("=".repeat(80));

    console.log(`📊 Datasets Tested: ${dataFiles.length}`);
    console.log(`🎉 Successful (21x achieved): ${successCount}`);
    console.log(`❌ Failed: ${dataFiles.length - successCount}`);
    console.log(
      `📈 Success Rate: ${((successCount / dataFiles.length) * 100).toFixed(
        1
      )}%`
    );

    // Best performance
    const bestResult = results.reduce((best, current) =>
      current.returnMultiplier > best.returnMultiplier ? current : best
    );

    console.log(`\n🏆 BEST PERFORMANCE:`);
    console.log(`   Dataset: ${bestResult.dataFile}`);
    console.log(
      `   Result: ${bestResult.finalBalance.toFixed(
        2
      )} USDT (${bestResult.returnMultiplier.toFixed(2)}x)`
    );
    console.log(
      `   Target: ${
        bestResult.targetAchieved ? "✅ ACHIEVED" : "❌ Not reached"
      }`
    );
    console.log(
      `   Trades: ${
        bestResult.totalTrades
      } | Win Rate: ${bestResult.winRate.toFixed(1)}%`
    );

    // Save results
    fs.writeFileSync(
      "smart_20x_results.json",
      JSON.stringify(
        {
          timestamp: new Date().toISOString(),
          summary: {
            datasetsTotal: dataFiles.length,
            successCount,
            successRate: (successCount / dataFiles.length) * 100,
          },
          bestResult,
          allResults: results,
        },
        null,
        2
      )
    );

    console.log(`\n💾 Results saved to smart_20x_results.json`);

    if (successCount > 0) {
      console.log(
        `\n🎉 SUCCESS! The SMART 20X strategy achieved the 21x target on ${successCount} dataset(s)!`
      );
      console.log(
        `🧠 Intelligent risk management and quality setups are the key to success!`
      );
    } else {
      console.log(
        `\n📈 Best performance: ${bestResult.returnMultiplier.toFixed(
          2
        )}x - Much better approach!`
      );
      console.log(
        `💡 The smart approach shows significant improvement over ultra-aggressive methods.`
      );
    }

    console.log("=".repeat(80));
  }
}

// Run the smart test
async function main() {
  const tester = new Smart20xStrategy();
  await tester.testAllDatasets();
}

main().catch(console.error);
