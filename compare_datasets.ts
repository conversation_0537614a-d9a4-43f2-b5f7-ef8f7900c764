import csv from "csv-parser";
import fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

// Types for our trading system
interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  highestPrice?: number;
  lowestPrice?: number;
}

interface Trade {
  entryTime: string;
  exitTime: string;
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  size: number;
  pnl: number;
  pnlPercent: number;
  leverage: number;
}

interface BacktestResult {
  dataset: string;
  initialBalance: number;
  finalBalance: number;
  totalReturn: number;
  totalReturnPercent: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Trade[];
  avgWin: number;
  avgLoss: number;
  profitFactor: number;
  maxConsecutiveLosses: number;
  maxConsecutiveWins: number;
}

class DatasetComparator {
  private data: CandleData[] = [];
  private balance: number;
  private initialBalance: number;
  private position: Position = {
    side: null,
    size: 0,
    entryPrice: 0,
    entryTime: "",
    leverage: 1,
  };
  private trades: Trade[] = [];
  private maxBalance: number;
  private maxDrawdown: number = 0;

  // ULTIMATE STRATEGY PARAMETERS for 8x target
  private readonly RSI_PERIOD = 14;
  private readonly EMA_FAST = 8;
  private readonly EMA_SLOW = 21;
  private readonly SMA_PERIOD = 50;
  private readonly RSI_OVERSOLD = 5; // EXTREME oversold
  private readonly RSI_OVERBOUGHT = 95; // EXTREME overbought
  private readonly LEVERAGE = 50; // MAXIMUM leverage
  private readonly RISK_PER_TRADE = 0.4; // 40% risk per trade
  private readonly STOP_LOSS_PERCENT = 0.006; // 0.6% ultra tight stop
  private readonly TAKE_PROFIT_PERCENT = 0.3; // 30% take profit (50:1 R:R)

  constructor(initialBalance: number = 100) {
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.maxBalance = initialBalance;
  }

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`Loaded ${results.length} candles from ${filePath}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private resetForNewDataset(): void {
    this.balance = this.initialBalance;
    this.maxBalance = this.initialBalance;
    this.maxDrawdown = 0;
    this.trades = [];
    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
    };
  }

  private calculateIndicators(
    index: number
  ): {
    rsi: number;
    emaFast: number;
    emaSlow: number;
    sma: number;
  } | null {
    if (index < Math.max(this.RSI_PERIOD, this.EMA_SLOW, this.SMA_PERIOD)) {
      return null;
    }

    const closes = this.data.slice(0, index + 1).map((d) => d.close);

    const rsiValues = RSI.calculate({
      values: closes,
      period: this.RSI_PERIOD,
    });
    const emaFastValues = EMA.calculate({
      values: closes,
      period: this.EMA_FAST,
    });
    const emaSlowValues = EMA.calculate({
      values: closes,
      period: this.EMA_SLOW,
    });
    const smaValues = SMA.calculate({
      values: closes,
      period: this.SMA_PERIOD,
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 0,
      emaFast: emaFastValues[emaFastValues.length - 1] || 0,
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || 0,
      sma: smaValues[smaValues.length - 1] || 0,
    };
  }

  private shouldEnterLong(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData
  ): boolean {
    // ULTIMATE MOMENTUM HUNTER STRATEGY

    // 1. EXTREME CONDITIONS
    const extremeOversold = indicators.rsi < this.RSI_OVERSOLD;
    const rsiRecovering = indicators.rsi > 3 && indicators.rsi < 20;

    // 2. EXPLOSIVE MOMENTUM
    const explosiveGreenCandle =
      candle.close > candle.open &&
      (candle.close - candle.open) / candle.open > 0.008; // 0.8%
    const ultraVolume = candle.volume > prevCandle.volume * 5.0; // 5x volume
    const ultraMomentum = candle.close > prevCandle.close * 1.01; // 1% momentum

    // 3. TREND POWER
    const bullishTrend = indicators.emaFast > indicators.emaSlow;
    const strongBullishTrend =
      indicators.emaFast > indicators.emaSlow &&
      indicators.emaSlow > indicators.sma;
    const breakoutAboveSMA =
      candle.close > indicators.sma && prevCandle.close <= indicators.sma;
    const emaGap =
      (indicators.emaFast - indicators.emaSlow) / indicators.emaSlow > 0.003;

    // 4. PATTERN POWER
    const hammerPattern =
      candle.close - candle.low > 4 * Math.abs(candle.close - candle.open) &&
      candle.close > candle.open;
    const engulfingPattern =
      candle.close > prevCandle.high && candle.open < prevCandle.close;
    const gapUp = candle.low > prevCandle.high;
    const priceNearHigh = candle.close > candle.high * 0.999;

    // ULTIMATE ENTRY CONDITIONS
    return (
      // TIER 1: EXPLOSIVE MOMENTUM
      (explosiveGreenCandle && ultraVolume && strongBullishTrend) ||
      (ultraMomentum && ultraVolume && bullishTrend && indicators.rsi < 30) ||
      // TIER 2: EXTREME REVERSALS
      (extremeOversold && explosiveGreenCandle && ultraVolume) ||
      (rsiRecovering && engulfingPattern && ultraVolume && emaGap) ||
      // TIER 3: BREAKOUT POWER
      (breakoutAboveSMA && ultraMomentum && ultraVolume) ||
      (gapUp && explosiveGreenCandle && ultraVolume) ||
      // TIER 4: PATTERN CONFIRMATIONS
      (hammerPattern && indicators.rsi < 15 && ultraVolume) ||
      (priceNearHigh &&
        explosiveGreenCandle &&
        ultraVolume &&
        strongBullishTrend)
    );
  }

  private shouldEnterShort(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData
  ): boolean {
    // ULTIMATE MOMENTUM HUNTER STRATEGY

    // 1. EXTREME CONDITIONS
    const extremeOverbought = indicators.rsi > this.RSI_OVERBOUGHT;
    const rsiWeakening = indicators.rsi < 97 && indicators.rsi > 80;

    // 2. EXPLOSIVE MOMENTUM
    const explosiveRedCandle =
      candle.close < candle.open &&
      (candle.open - candle.close) / candle.open > 0.008; // 0.8%
    const ultraVolume = candle.volume > prevCandle.volume * 5.0; // 5x volume
    const ultraMomentum = candle.close < prevCandle.close * 0.99; // 1% downward momentum

    // 3. TREND POWER
    const bearishTrend = indicators.emaFast < indicators.emaSlow;
    const strongBearishTrend =
      indicators.emaFast < indicators.emaSlow &&
      indicators.emaSlow < indicators.sma;
    const breakdownBelowSMA =
      candle.close < indicators.sma && prevCandle.close >= indicators.sma;
    const emaGap =
      (indicators.emaSlow - indicators.emaFast) / indicators.emaFast > 0.003;

    // 4. PATTERN POWER
    const shootingStarPattern =
      candle.high - candle.close > 4 * Math.abs(candle.close - candle.open) &&
      candle.close < candle.open;
    const bearishEngulfing =
      candle.close < prevCandle.low && candle.open > prevCandle.close;
    const gapDown = candle.high < prevCandle.low;
    const priceNearLow = candle.close < candle.low * 1.001;

    // ULTIMATE ENTRY CONDITIONS
    return (
      // TIER 1: EXPLOSIVE MOMENTUM
      (explosiveRedCandle && ultraVolume && strongBearishTrend) ||
      (ultraMomentum && ultraVolume && bearishTrend && indicators.rsi > 70) ||
      // TIER 2: EXTREME REVERSALS
      (extremeOverbought && explosiveRedCandle && ultraVolume) ||
      (rsiWeakening && bearishEngulfing && ultraVolume && emaGap) ||
      // TIER 3: BREAKDOWN POWER
      (breakdownBelowSMA && ultraMomentum && ultraVolume) ||
      (gapDown && explosiveRedCandle && ultraVolume) ||
      // TIER 4: PATTERN CONFIRMATIONS
      (shootingStarPattern && indicators.rsi > 85 && ultraVolume) ||
      (priceNearLow && explosiveRedCandle && ultraVolume && strongBearishTrend)
    );
  }

  private shouldExitPosition(candle: CandleData, indicators: any): boolean {
    if (!this.position.side) return false;

    const currentPrice = candle.close;
    const entryPrice = this.position.entryPrice;

    // Update highest/lowest prices for trailing stop
    if (this.position.side === "LONG") {
      if (
        !this.position.highestPrice ||
        currentPrice > this.position.highestPrice
      ) {
        this.position.highestPrice = currentPrice;
      }
    } else {
      if (
        !this.position.lowestPrice ||
        currentPrice < this.position.lowestPrice
      ) {
        this.position.lowestPrice = currentPrice;
      }
    }

    const pnlPercent =
      this.position.side === "LONG"
        ? (currentPrice - entryPrice) / entryPrice
        : (entryPrice - currentPrice) / entryPrice;

    // Hard stop loss
    if (pnlPercent <= -this.STOP_LOSS_PERCENT) {
      return true;
    }

    // Take profit at target
    if (pnlPercent >= this.TAKE_PROFIT_PERCENT) {
      return true;
    }

    // Aggressive trailing stop - activate after 5% profit
    if (pnlPercent > 0.05) {
      const trailingStopPercent = 0.03; // 3% trailing stop

      if (this.position.side === "LONG" && this.position.highestPrice) {
        const trailingStopPrice =
          this.position.highestPrice * (1 - trailingStopPercent);
        if (currentPrice <= trailingStopPrice) {
          return true;
        }
      }

      if (this.position.side === "SHORT" && this.position.lowestPrice) {
        const trailingStopPrice =
          this.position.lowestPrice * (1 + trailingStopPercent);
        if (currentPrice >= trailingStopPrice) {
          return true;
        }
      }
    }

    // RSI extreme reversal signals
    if (this.position.side === "LONG" && indicators.rsi > 85) {
      return true;
    }

    if (this.position.side === "SHORT" && indicators.rsi < 15) {
      return true;
    }

    return false;
  }

  private calculatePositionSize(): number {
    // ULTIMATE AGGRESSION for 8x target
    let riskPercent = this.RISK_PER_TRADE;

    // Extreme scaling for maximum compound growth
    if (this.balance > this.initialBalance * 7) {
      riskPercent = 0.3; // Still very aggressive when 7x ahead
    } else if (this.balance > this.initialBalance * 5) {
      riskPercent = 0.35; // Aggressive when 5x ahead
    } else if (this.balance > this.initialBalance * 3) {
      riskPercent = 0.4; // Very aggressive when 3x ahead
    } else if (this.balance > this.initialBalance * 2) {
      riskPercent = 0.45; // Ultra aggressive when 2x ahead
    } else {
      riskPercent = 0.5; // MAXIMUM aggression when behind
    }

    const currentRiskAmount = this.balance * riskPercent;
    const maxRiskAmount = this.balance * 0.7; // Max 70% of current balance

    const riskAmount = Math.min(currentRiskAmount, maxRiskAmount);
    return riskAmount * this.LEVERAGE;
  }

  private openPosition(
    side: "LONG" | "SHORT",
    price: number,
    time: string
  ): void {
    const size = this.calculatePositionSize();

    this.position = {
      side,
      size,
      entryPrice: price,
      entryTime: time,
      leverage: this.LEVERAGE,
      highestPrice: side === "LONG" ? price : undefined,
      lowestPrice: side === "SHORT" ? price : undefined,
    };

    console.log(
      `Opened ${side} position at ${price} with size ${size.toFixed(
        4
      )} (${time})`
    );
  }

  private closePosition(price: number, time: string): void {
    if (!this.position.side) return;

    const priceChangePercent =
      this.position.side === "LONG"
        ? (price - this.position.entryPrice) / this.position.entryPrice
        : (this.position.entryPrice - price) / this.position.entryPrice;

    const positionValue = this.position.size / this.LEVERAGE;
    const pnl = positionValue * priceChangePercent * this.LEVERAGE;
    const pnlPercent = priceChangePercent * this.LEVERAGE * 100;

    // Cap maximum loss to prevent account blowup
    const maxLoss = this.balance * 0.06; // Max 6% loss per trade
    const cappedPnl = Math.max(pnl, -maxLoss);

    this.balance += cappedPnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }

    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade: Trade = {
      entryTime: this.position.entryTime,
      exitTime: time,
      side: this.position.side,
      entryPrice: this.position.entryPrice,
      exitPrice: price,
      size: this.position.size,
      pnl: cappedPnl,
      pnlPercent,
      leverage: this.position.leverage,
    };

    this.trades.push(trade);

    console.log(
      `Closed ${
        this.position.side
      } position at ${price}. PnL: ${cappedPnl.toFixed(
        2
      )} USDT (${pnlPercent.toFixed(2)}%). Balance: ${this.balance.toFixed(
        2
      )} USDT`
    );

    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      highestPrice: undefined,
      lowestPrice: undefined,
    };
  }

  async runBacktest(datasetName: string): Promise<BacktestResult> {
    console.log(
      `\n🚀 Starting ULTIMATE MOMENTUM HUNTER backtest on ${datasetName}...`
    );
    console.log(`Initial balance: ${this.initialBalance} USDT`);
    console.log(`Target: ${this.initialBalance * 8} USDT (8x return)`);

    for (let i = 1; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      if (!indicators) continue;

      // Check for exit signals if position exists
      if (this.position.side && this.shouldExitPosition(candle, indicators)) {
        this.closePosition(candle.close, candle.time);
      }

      // Check for entry signals if no position
      if (!this.position.side) {
        if (this.shouldEnterLong(candle, indicators, prevCandle)) {
          this.openPosition("LONG", candle.close, candle.time);
        } else if (this.shouldEnterShort(candle, indicators, prevCandle)) {
          this.openPosition("SHORT", candle.close, candle.time);
        }
      }

      // Emergency exit if balance drops too low
      if (this.balance < this.initialBalance * 0.1) {
        console.log("⚠️ Emergency exit: Balance dropped below 10% of initial");
        if (this.position.side) {
          this.closePosition(candle.close, candle.time);
        }
        break;
      }
    }

    // Close any remaining position
    if (this.position.side) {
      const lastCandle = this.data[this.data.length - 1];
      this.closePosition(lastCandle.close, lastCandle.time);
    }

    const winningTrades = this.trades.filter((t) => t.pnl > 0);
    const losingTrades = this.trades.filter((t) => t.pnl <= 0);
    const totalReturn = this.balance - this.initialBalance;
    const totalReturnPercent = (totalReturn / this.initialBalance) * 100;

    // Calculate advanced metrics
    const avgWin =
      winningTrades.length > 0
        ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) /
          winningTrades.length
        : 0;
    const avgLoss =
      losingTrades.length > 0
        ? Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0)) /
          losingTrades.length
        : 0;
    const profitFactor =
      avgLoss > 0
        ? (avgWin * winningTrades.length) / (avgLoss * losingTrades.length)
        : 0;

    // Calculate consecutive wins/losses
    let maxConsecutiveWins = 0;
    let maxConsecutiveLosses = 0;
    let currentWins = 0;
    let currentLosses = 0;

    for (const trade of this.trades) {
      if (trade.pnl > 0) {
        currentWins++;
        currentLosses = 0;
        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWins);
      } else {
        currentLosses++;
        currentWins = 0;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLosses);
      }
    }

    // Calculate Sharpe ratio (simplified)
    const returns = this.trades.map((t) => t.pnlPercent / 100);
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const returnStdDev = Math.sqrt(
      returns.reduce((a, b) => a + Math.pow(b - avgReturn, 2), 0) /
        returns.length
    );
    const sharpeRatio = returnStdDev > 0 ? avgReturn / returnStdDev : 0;

    return {
      dataset: datasetName,
      initialBalance: this.initialBalance,
      finalBalance: this.balance,
      totalReturn,
      totalReturnPercent,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate:
        this.trades.length > 0
          ? (winningTrades.length / this.trades.length) * 100
          : 0,
      maxDrawdown: this.maxDrawdown * 100,
      sharpeRatio,
      trades: this.trades,
      avgWin,
      avgLoss,
      profitFactor,
      maxConsecutiveLosses,
      maxConsecutiveWins,
    };
  }

  async compareDatasets(): Promise<void> {
    const datasets = [
      {
        name: "Sep-Dec 2024",
        path: "./data/historical_data_9_12_2024_15m.csv",
      },
      { name: "March 2025", path: "./data/historical_data_mar_15m.csv" },
      { name: "June 2025", path: "./data/historical_data_jun_15m.csv" },
    ];

    const results: BacktestResult[] = [];

    for (const dataset of datasets) {
      console.log(`\n${"=".repeat(60)}`);
      console.log(`📊 TESTING DATASET: ${dataset.name}`);
      console.log(`${"=".repeat(60)}`);

      this.resetForNewDataset();
      await this.loadData(dataset.path);
      const result = await this.runBacktest(dataset.name);
      results.push(result);

      console.log(`\n🎯 ${dataset.name} Results:`);
      console.log(`Final Balance: ${result.finalBalance.toFixed(2)} USDT`);
      console.log(
        `Total Return: ${result.totalReturn.toFixed(
          2
        )} USDT (${result.totalReturnPercent.toFixed(2)}%)`
      );
      console.log(`Total Trades: ${result.totalTrades}`);
      console.log(`Win Rate: ${result.winRate.toFixed(2)}%`);
      console.log(`Max Drawdown: ${result.maxDrawdown.toFixed(2)}%`);
      console.log(`Profit Factor: ${result.profitFactor.toFixed(2)}`);
      console.log(`Avg Win: ${result.avgWin.toFixed(2)} USDT`);
      console.log(`Avg Loss: ${result.avgLoss.toFixed(2)} USDT`);
      console.log(`Max Consecutive Wins: ${result.maxConsecutiveWins}`);
      console.log(`Max Consecutive Losses: ${result.maxConsecutiveLosses}`);

      if (result.finalBalance >= this.initialBalance * 8) {
        console.log("🎉 TARGET ACHIEVED! 8x return reached!");
      } else {
        console.log(
          `📊 Progress: ${(result.finalBalance / this.initialBalance).toFixed(
            2
          )}x of 8x target`
        );
      }
    }

    // Detailed comparison
    console.log(`\n${"=".repeat(100)}`);
    console.log(`📈 COMPREHENSIVE 3-DATASET COMPARISON ANALYSIS`);
    console.log(`${"=".repeat(100)}`);

    const [sepDecResult, marchResult, juneResult] = results;

    console.log(`\n🔍 PERFORMANCE COMPARISON TABLE:`);
    console.log(
      `┌─────────────────────────┬─────────────────┬─────────────────┬─────────────────┐`
    );
    console.log(
      `│ Metric                  │ Sep-Dec 2024    │ March 2025      │ June 2025       │`
    );
    console.log(
      `├─────────────────────────┼─────────────────┼─────────────────┼─────────────────┤`
    );
    console.log(
      `│ Final Balance (USDT)    │ ${sepDecResult.finalBalance
        .toFixed(2)
        .padStart(15)} │ ${marchResult.finalBalance
        .toFixed(2)
        .padStart(15)} │ ${juneResult.finalBalance.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Return (%)              │ ${sepDecResult.totalReturnPercent
        .toFixed(2)
        .padStart(15)} │ ${marchResult.totalReturnPercent
        .toFixed(2)
        .padStart(15)} │ ${juneResult.totalReturnPercent
        .toFixed(2)
        .padStart(15)} │`
    );
    console.log(
      `│ Total Trades            │ ${sepDecResult.totalTrades
        .toString()
        .padStart(15)} │ ${marchResult.totalTrades
        .toString()
        .padStart(15)} │ ${juneResult.totalTrades.toString().padStart(15)} │`
    );
    console.log(
      `│ Win Rate (%)            │ ${sepDecResult.winRate
        .toFixed(2)
        .padStart(15)} │ ${marchResult.winRate
        .toFixed(2)
        .padStart(15)} │ ${juneResult.winRate.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Max Drawdown (%)        │ ${sepDecResult.maxDrawdown
        .toFixed(2)
        .padStart(15)} │ ${marchResult.maxDrawdown
        .toFixed(2)
        .padStart(15)} │ ${juneResult.maxDrawdown.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Profit Factor           │ ${sepDecResult.profitFactor
        .toFixed(2)
        .padStart(15)} │ ${marchResult.profitFactor
        .toFixed(2)
        .padStart(15)} │ ${juneResult.profitFactor.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Sharpe Ratio            │ ${sepDecResult.sharpeRatio
        .toFixed(2)
        .padStart(15)} │ ${marchResult.sharpeRatio
        .toFixed(2)
        .padStart(15)} │ ${juneResult.sharpeRatio.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Avg Win (USDT)          │ ${sepDecResult.avgWin
        .toFixed(2)
        .padStart(15)} │ ${marchResult.avgWin
        .toFixed(2)
        .padStart(15)} │ ${juneResult.avgWin.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Avg Loss (USDT)         │ ${sepDecResult.avgLoss
        .toFixed(2)
        .padStart(15)} │ ${marchResult.avgLoss
        .toFixed(2)
        .padStart(15)} │ ${juneResult.avgLoss.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Max Consecutive Wins    │ ${sepDecResult.maxConsecutiveWins
        .toString()
        .padStart(15)} │ ${marchResult.maxConsecutiveWins
        .toString()
        .padStart(15)} │ ${juneResult.maxConsecutiveWins
        .toString()
        .padStart(15)} │`
    );
    console.log(
      `│ Max Consecutive Losses  │ ${sepDecResult.maxConsecutiveLosses
        .toString()
        .padStart(15)} │ ${marchResult.maxConsecutiveLosses
        .toString()
        .padStart(15)} │ ${juneResult.maxConsecutiveLosses
        .toString()
        .padStart(15)} │`
    );
    console.log(
      `│ Progress to 8x Target   │ ${(
        sepDecResult.finalBalance / this.initialBalance
      )
        .toFixed(2)
        .padStart(15)} │ ${(marchResult.finalBalance / this.initialBalance)
        .toFixed(2)
        .padStart(15)} │ ${(juneResult.finalBalance / this.initialBalance)
        .toFixed(2)
        .padStart(15)} │`
    );
    console.log(
      `└─────────────────────────┴─────────────────┴─────────────────┴─────────────────┘`
    );

    // Determine best performing dataset
    const allResults = [sepDecResult, marchResult, juneResult];
    const bestResult = allResults.reduce((best, current) =>
      current.finalBalance > best.finalBalance ? current : best
    );
    const worstResult = allResults.reduce((worst, current) =>
      current.finalBalance < worst.finalBalance ? current : worst
    );

    console.log(`\n🏆 PERFORMANCE RANKING:`);
    const sortedResults = [...allResults].sort(
      (a, b) => b.finalBalance - a.finalBalance
    );
    sortedResults.forEach((result, index) => {
      const medal = index === 0 ? "🥇" : index === 1 ? "🥈" : "🥉";
      const targetProgress = (
        result.finalBalance / this.initialBalance
      ).toFixed(2);
      console.log(
        `${medal} ${index + 1}. ${
          result.dataset
        }: ${result.finalBalance.toFixed(
          2
        )} USDT (${result.totalReturnPercent.toFixed(
          2
        )}%) - ${targetProgress}x of 8x target`
      );
    });

    console.log(`\n📊 STATISTICAL ANALYSIS:`);
    console.log(
      `🎯 Best Performance: ${
        bestResult.dataset
      } - ${bestResult.finalBalance.toFixed(
        2
      )} USDT (${bestResult.totalReturnPercent.toFixed(2)}%)`
    );
    console.log(
      `📉 Worst Performance: ${
        worstResult.dataset
      } - ${worstResult.finalBalance.toFixed(
        2
      )} USDT (${worstResult.totalReturnPercent.toFixed(2)}%)`
    );
    console.log(
      `📈 Performance Range: ${(
        bestResult.finalBalance - worstResult.finalBalance
      ).toFixed(2)} USDT`
    );

    const avgReturn =
      allResults.reduce((sum, r) => sum + r.totalReturnPercent, 0) /
      allResults.length;
    const avgWinRate =
      allResults.reduce((sum, r) => sum + r.winRate, 0) / allResults.length;
    const avgDrawdown =
      allResults.reduce((sum, r) => sum + r.maxDrawdown, 0) / allResults.length;

    console.log(`📊 Average Return: ${avgReturn.toFixed(2)}%`);
    console.log(`📊 Average Win Rate: ${avgWinRate.toFixed(2)}%`);
    console.log(`📊 Average Max Drawdown: ${avgDrawdown.toFixed(2)}%`);

    // Market condition analysis
    console.log(`\n🔍 MARKET CONDITION INSIGHTS:`);
    if (bestResult === sepDecResult) {
      console.log(
        `🚀 Sep-Dec 2024 provided the most favorable conditions for momentum trading`
      );
    } else if (bestResult === marchResult) {
      console.log(
        `🚀 March 2025 provided the most favorable conditions for momentum trading`
      );
    } else {
      console.log(
        `🚀 June 2025 provided the most favorable conditions for momentum trading`
      );
    }

    // Save comparison results
    const comparisonData = {
      comparison: {
        sepDecResult,
        marchResult,
        juneResult,
        winner: bestResult.dataset,
        bestReturn: bestResult.totalReturnPercent,
        bestBalance: bestResult.finalBalance,
        averageReturn: avgReturn,
        averageWinRate: avgWinRate,
        averageDrawdown: avgDrawdown,
        performanceRange: bestResult.finalBalance - worstResult.finalBalance,
      },
    };

    fs.writeFileSync(
      "./dataset_comparison_results.json",
      JSON.stringify(comparisonData, null, 2)
    );
    console.log(
      "\n📁 Comparison results saved to dataset_comparison_results.json"
    );
  }
}

// Main execution
async function main() {
  const comparator = new DatasetComparator(100);

  try {
    await comparator.compareDatasets();
  } catch (error) {
    console.error("Error running comparison:", error);
  }
}

// Run the comparison
main();
