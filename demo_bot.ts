import { TradeLogger } from './trade_logger.js';
import { WebServer } from './web_server.js';

/**
 * Demo Trading Bot - Shows the logging and web dashboard functionality
 * without requiring real Binance API keys
 */
class DemoTradingBot {
  private logger: TradeLogger;
  private webServer: WebServer;
  private isRunning = false;
  private balance = 100;
  private demoPrice = 45000;
  private tradeCount = 0;

  constructor() {
    this.logger = new TradeLogger();
    this.webServer = new WebServer(3000, this.logger.getWebLogsPath());
  }

  async start(): Promise<void> {
    console.log('🚀 Starting Demo Trading Bot...');
    
    // Start web server
    await this.webServer.start();
    
    this.isRunning = true;
    this.logger.info('✅ Demo bot started successfully!');
    this.logger.info('💰 Initial balance: 100 USDT');
    this.logger.info('🌐 Dashboard: http://localhost:3000');
    
    // Start demo trading simulation
    this.startDemoTrading();
  }

  private startDemoTrading(): void {
    // Simulate market data and trades every 10 seconds
    const interval = setInterval(() => {
      if (!this.isRunning) {
        clearInterval(interval);
        return;
      }

      this.simulateMarketUpdate();
      
      // Randomly simulate trades
      if (Math.random() < 0.3) { // 30% chance of trade
        this.simulateTrade();
      }
      
    }, 10000);
  }

  private simulateMarketUpdate(): void {
    // Simulate price movement
    const change = (Math.random() - 0.5) * 1000; // ±500 price change
    this.demoPrice += change;
    this.demoPrice = Math.max(this.demoPrice, 30000); // Keep price reasonable
    
    this.logger.info(
      `💹 BTCUSDT: ${this.demoPrice.toFixed(2)} | Market: TRENDING | ` +
      `Balance: ${this.balance.toFixed(2)} USDT | Trades: ${this.tradeCount}`,
      'BTCUSDT'
    );
  }

  private simulateTrade(): void {
    const side = Math.random() > 0.5 ? 'LONG' : 'SHORT';
    const entryPrice = this.demoPrice;
    const quantity = 0.001;
    const reason = this.getRandomReason();
    
    // Simulate opening position
    this.logger.openPosition(
      'BTCUSDT',
      side,
      entryPrice,
      quantity,
      reason,
      this.balance,
      50, // leverage
      'TRENDING'
    );
    
    // Simulate closing position after 30-60 seconds
    setTimeout(() => {
      const exitPrice = entryPrice + (Math.random() - 0.5) * 2000; // Random exit
      const pnl = side === 'LONG' 
        ? (exitPrice - entryPrice) * quantity * 50 // 50x leverage
        : (entryPrice - exitPrice) * quantity * 50;
      
      const pnlPercent = (pnl / (entryPrice * quantity)) * 100;
      
      this.balance += pnl;
      this.tradeCount++;
      
      this.logger.closePosition(
        'BTCUSDT',
        side,
        exitPrice,
        quantity,
        pnl,
        pnlPercent,
        this.balance,
        this.getRandomExitReason()
      );
      
    }, 30000 + Math.random() * 30000); // 30-60 seconds
  }

  private getRandomReason(): string {
    const reasons = [
      'RSI_OVERSOLD_EXPLOSIVE',
      'EMA_GOLDEN_CROSS',
      'SMA_BREAKOUT_TREND',
      'VOLUME_SPIKE_MOMENTUM',
      'BULLISH_ENGULFING',
      'TREND_CONTINUATION'
    ];
    return reasons[Math.floor(Math.random() * reasons.length)];
  }

  private getRandomExitReason(): string {
    const reasons = [
      'TAKE_PROFIT',
      'STOP_LOSS',
      'RSI_OVERBOUGHT_EXIT',
      'EMA_DEATH_CROSS',
      'TRAILING_STOP',
      'VOLUME_DECLINE'
    ];
    return reasons[Math.floor(Math.random() * reasons.length)];
  }

  async stop(): Promise<void> {
    this.logger.info('🛑 Stopping demo bot...');
    this.isRunning = false;
    await this.webServer.stop();
    this.logger.info('✅ Demo bot stopped successfully');
  }
}

// Main execution
async function main() {
  const bot = new DemoTradingBot();

  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    await bot.stop();
    process.exit(0);
  });

  try {
    await bot.start();
    
    console.log('\n📊 Demo Bot Features:');
    console.log('• Simulated trading with realistic price movements');
    console.log('• Live web dashboard at http://localhost:3000');
    console.log('• Comprehensive trade logging');
    console.log('• No real API keys required');
    console.log('\n🛑 Press Ctrl+C to stop the demo\n');
    
    // Keep the process running
    process.stdin.resume();
    
  } catch (error) {
    console.error('❌ Failed to start demo bot:', error);
    process.exit(1);
  }
}

// Run the demo
main().catch(console.error);
