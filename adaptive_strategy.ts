import csv from "csv-parser";
import fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

// Types for our trading system
interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  highestPrice?: number;
  lowestPrice?: number;
}

interface Trade {
  entryTime: string;
  exitTime: string;
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  size: number;
  pnl: number;
  pnlPercent: number;
  leverage: number;
}

interface BacktestResult {
  dataset: string;
  initialBalance: number;
  finalBalance: number;
  totalReturn: number;
  totalReturnPercent: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Trade[];
  avgWin: number;
  avgLoss: number;
  profitFactor: number;
  maxConsecutiveLosses: number;
  maxConsecutiveWins: number;
}

interface MarketRegime {
  volatility: number;
  trend: number;
  volume: number;
  regime: "HIGH_VOLATILITY" | "MEDIUM_VOLATILITY" | "LOW_VOLATILITY";
}

class AdaptiveUltimateStrategy {
  private data: CandleData[] = [];
  private balance: number;
  private initialBalance: number;
  private position: Position = {
    side: null,
    size: 0,
    entryPrice: 0,
    entryTime: "",
    leverage: 1,
  };
  private trades: Trade[] = [];
  private maxBalance: number;
  private maxDrawdown: number = 0;
  private marketRegime: MarketRegime = {
    volatility: 0,
    trend: 0,
    volume: 0,
    regime: "MEDIUM_VOLATILITY",
  };

  // ADAPTIVE STRATEGY PARAMETERS - Dynamic based on market conditions
  private readonly RSI_PERIOD = 14;
  private readonly EMA_FAST = 8;
  private readonly EMA_SLOW = 21;
  private readonly SMA_PERIOD = 50;
  private readonly VOLATILITY_PERIOD = 20;

  // Dynamic parameters that adapt to market conditions
  private RSI_OVERSOLD = 15;
  private RSI_OVERBOUGHT = 85;
  private LEVERAGE = 50;
  private RISK_PER_TRADE = 0.35;
  private STOP_LOSS_PERCENT = 0.008;
  private TAKE_PROFIT_PERCENT = 0.25;
  private VOLUME_MULTIPLIER = 3.0;
  private MOMENTUM_THRESHOLD = 0.006;

  constructor(initialBalance: number = 100) {
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.maxBalance = initialBalance;
  }

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`Loaded ${results.length} candles from ${filePath}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private resetForNewDataset(): void {
    this.balance = this.initialBalance;
    this.maxBalance = this.initialBalance;
    this.maxDrawdown = 0;
    this.trades = [];
    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
    };
  }

  private calculateMarketRegime(index: number): MarketRegime {
    if (index < this.VOLATILITY_PERIOD + this.SMA_PERIOD) {
      return this.marketRegime;
    }

    const recentCandles = this.data.slice(
      index - this.VOLATILITY_PERIOD,
      index + 1
    );
    const closes = recentCandles.map((c) => c.close);
    const volumes = recentCandles.map((c) => c.volume);

    // Calculate volatility (standard deviation of returns)
    const returns = [];
    for (let i = 1; i < closes.length; i++) {
      returns.push((closes[i] - closes[i - 1]) / closes[i - 1]);
    }
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const volatility = Math.sqrt(
      returns.reduce((a, b) => a + Math.pow(b - avgReturn, 2), 0) /
        returns.length
    );

    // Calculate trend strength
    const smaValues = SMA.calculate({
      values: closes,
      period: this.VOLATILITY_PERIOD,
    });
    const currentSMA = smaValues[smaValues.length - 1];
    const pastSMA = smaValues[Math.max(0, smaValues.length - 10)];
    const trend = Math.abs((currentSMA - pastSMA) / pastSMA);

    // Calculate volume strength
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
    const recentVolume = volumes.slice(-5).reduce((a, b) => a + b, 0) / 5;
    const volumeRatio = recentVolume / avgVolume;

    // Determine regime
    let regime: "HIGH_VOLATILITY" | "MEDIUM_VOLATILITY" | "LOW_VOLATILITY";
    if (volatility > 0.025 && volumeRatio > 1.2) {
      regime = "HIGH_VOLATILITY";
    } else if (volatility > 0.015 || volumeRatio > 1.1) {
      regime = "MEDIUM_VOLATILITY";
    } else {
      regime = "LOW_VOLATILITY";
    }

    return {
      volatility,
      trend,
      volume: volumeRatio,
      regime,
    };
  }

  private adaptParametersToMarket(regime: MarketRegime): void {
    switch (regime.regime) {
      case "HIGH_VOLATILITY":
        // March 2025 conditions - Maximum aggression
        this.RSI_OVERSOLD = 5;
        this.RSI_OVERBOUGHT = 95;
        this.LEVERAGE = 60;
        this.RISK_PER_TRADE = 0.45;
        this.STOP_LOSS_PERCENT = 0.006;
        this.TAKE_PROFIT_PERCENT = 0.35;
        this.VOLUME_MULTIPLIER = 2.5;
        this.MOMENTUM_THRESHOLD = 0.008;
        break;

      case "MEDIUM_VOLATILITY":
        // June 2025 conditions - Balanced approach
        this.RSI_OVERSOLD = 10;
        this.RSI_OVERBOUGHT = 90;
        this.LEVERAGE = 45;
        this.RISK_PER_TRADE = 0.35;
        this.STOP_LOSS_PERCENT = 0.008;
        this.TAKE_PROFIT_PERCENT = 0.25;
        this.VOLUME_MULTIPLIER = 3.0;
        this.MOMENTUM_THRESHOLD = 0.006;
        break;

      case "LOW_VOLATILITY":
        // Sep-Dec 2024 conditions - Conservative but frequent
        this.RSI_OVERSOLD = 20;
        this.RSI_OVERBOUGHT = 80;
        this.LEVERAGE = 35;
        this.RISK_PER_TRADE = 0.25;
        this.STOP_LOSS_PERCENT = 0.012;
        this.TAKE_PROFIT_PERCENT = 0.15;
        this.VOLUME_MULTIPLIER = 4.0;
        this.MOMENTUM_THRESHOLD = 0.004;
        break;
    }
  }

  private calculateIndicators(
    index: number
  ): {
    rsi: number;
    emaFast: number;
    emaSlow: number;
    sma: number;
  } | null {
    if (index < Math.max(this.RSI_PERIOD, this.EMA_SLOW, this.SMA_PERIOD)) {
      return null;
    }

    const closes = this.data.slice(0, index + 1).map((d) => d.close);

    const rsiValues = RSI.calculate({
      values: closes,
      period: this.RSI_PERIOD,
    });
    const emaFastValues = EMA.calculate({
      values: closes,
      period: this.EMA_FAST,
    });
    const emaSlowValues = EMA.calculate({
      values: closes,
      period: this.EMA_SLOW,
    });
    const smaValues = SMA.calculate({
      values: closes,
      period: this.SMA_PERIOD,
    });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 0,
      emaFast: emaFastValues[emaFastValues.length - 1] || 0,
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || 0,
      sma: smaValues[smaValues.length - 1] || 0,
    };
  }

  private shouldEnterLong(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData,
    regime: MarketRegime
  ): boolean {
    // ADAPTIVE MOMENTUM HUNTER - Adjusts to market conditions

    // 1. REGIME-SPECIFIC CONDITIONS
    const extremeOversold = indicators.rsi < this.RSI_OVERSOLD;
    const rsiRecovering =
      indicators.rsi > this.RSI_OVERSOLD &&
      indicators.rsi < this.RSI_OVERSOLD + 15;

    // 2. ADAPTIVE MOMENTUM PATTERNS
    const explosiveGreenCandle =
      candle.close > candle.open &&
      (candle.close - candle.open) / candle.open > this.MOMENTUM_THRESHOLD;
    const ultraVolume =
      candle.volume > prevCandle.volume * this.VOLUME_MULTIPLIER;
    const strongMomentum =
      candle.close > prevCandle.close * (1 + this.MOMENTUM_THRESHOLD);

    // 3. TREND ALIGNMENT
    const bullishTrend = indicators.emaFast > indicators.emaSlow;
    const strongBullishTrend =
      indicators.emaFast > indicators.emaSlow &&
      indicators.emaSlow > indicators.sma;
    const breakoutAboveSMA =
      candle.close > indicators.sma && prevCandle.close <= indicators.sma;
    const emaGap =
      (indicators.emaFast - indicators.emaSlow) / indicators.emaSlow > 0.002;

    // 4. ENHANCED PATTERNS FOR DIFFERENT REGIMES
    const hammerPattern =
      candle.close - candle.low > 3 * Math.abs(candle.close - candle.open) &&
      candle.close > candle.open;
    const engulfingPattern =
      candle.close > prevCandle.high && candle.open < prevCandle.close;
    const gapUp = candle.low > prevCandle.high;
    const priceAcceleration = candle.close > candle.high * 0.998;

    // 5. VOLUME CONFIRMATION
    const volumeSpike =
      candle.volume > prevCandle.volume * (this.VOLUME_MULTIPLIER * 0.8);
    const volumeBreakout =
      candle.volume > prevCandle.volume * (this.VOLUME_MULTIPLIER * 1.2);

    // REGIME-ADAPTIVE ENTRY LOGIC
    switch (regime.regime) {
      case "HIGH_VOLATILITY":
        // March 2025 style - Focus on explosive moves
        return (
          (explosiveGreenCandle && ultraVolume && strongBullishTrend) ||
          (extremeOversold && explosiveGreenCandle && volumeBreakout) ||
          (breakoutAboveSMA &&
            strongMomentum &&
            ultraVolume &&
            indicators.rsi < 30) ||
          (gapUp && explosiveGreenCandle && volumeBreakout)
        );

      case "MEDIUM_VOLATILITY":
        // June 2025 style - Balanced approach with more signals
        return (
          (explosiveGreenCandle &&
            volumeSpike &&
            bullishTrend &&
            indicators.rsi < 40) ||
          (rsiRecovering && engulfingPattern && ultraVolume && emaGap) ||
          (breakoutAboveSMA && strongMomentum && volumeSpike) ||
          (hammerPattern && indicators.rsi < 25 && volumeSpike) ||
          (strongBullishTrend &&
            explosiveGreenCandle &&
            volumeSpike &&
            indicators.rsi < 50)
        );

      case "LOW_VOLATILITY":
        // Sep-Dec 2024 style - More frequent, smaller opportunities
        return (
          (explosiveGreenCandle && volumeSpike && bullishTrend) ||
          (rsiRecovering && bullishTrend && strongMomentum && volumeSpike) ||
          (breakoutAboveSMA && strongMomentum && volumeSpike) ||
          (engulfingPattern && bullishTrend && volumeSpike) ||
          (priceAcceleration &&
            explosiveGreenCandle &&
            volumeSpike &&
            strongBullishTrend) ||
          (hammerPattern && indicators.rsi < 30 && volumeSpike)
        );

      default:
        return false;
    }
  }

  private shouldEnterShort(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData,
    regime: MarketRegime
  ): boolean {
    // ADAPTIVE MOMENTUM HUNTER - Adjusts to market conditions

    // 1. REGIME-SPECIFIC CONDITIONS
    const extremeOverbought = indicators.rsi > this.RSI_OVERBOUGHT;
    const rsiWeakening =
      indicators.rsi < this.RSI_OVERBOUGHT &&
      indicators.rsi > this.RSI_OVERBOUGHT - 15;

    // 2. ADAPTIVE MOMENTUM PATTERNS
    const explosiveRedCandle =
      candle.close < candle.open &&
      (candle.open - candle.close) / candle.open > this.MOMENTUM_THRESHOLD;
    const ultraVolume =
      candle.volume > prevCandle.volume * this.VOLUME_MULTIPLIER;
    const strongMomentum =
      candle.close < prevCandle.close * (1 - this.MOMENTUM_THRESHOLD);

    // 3. TREND ALIGNMENT
    const bearishTrend = indicators.emaFast < indicators.emaSlow;
    const strongBearishTrend =
      indicators.emaFast < indicators.emaSlow &&
      indicators.emaSlow < indicators.sma;
    const breakdownBelowSMA =
      candle.close < indicators.sma && prevCandle.close >= indicators.sma;
    const emaGap =
      (indicators.emaSlow - indicators.emaFast) / indicators.emaFast > 0.002;

    // 4. ENHANCED PATTERNS FOR DIFFERENT REGIMES
    const shootingStarPattern =
      candle.high - candle.close > 3 * Math.abs(candle.close - candle.open) &&
      candle.close < candle.open;
    const bearishEngulfing =
      candle.close < prevCandle.low && candle.open > prevCandle.close;
    const gapDown = candle.high < prevCandle.low;
    const priceAcceleration = candle.close < candle.low * 1.002;

    // 5. VOLUME CONFIRMATION
    const volumeSpike =
      candle.volume > prevCandle.volume * (this.VOLUME_MULTIPLIER * 0.8);
    const volumeBreakout =
      candle.volume > prevCandle.volume * (this.VOLUME_MULTIPLIER * 1.2);

    // REGIME-ADAPTIVE ENTRY LOGIC
    switch (regime.regime) {
      case "HIGH_VOLATILITY":
        // March 2025 style - Focus on explosive moves
        return (
          (explosiveRedCandle && ultraVolume && strongBearishTrend) ||
          (extremeOverbought && explosiveRedCandle && volumeBreakout) ||
          (breakdownBelowSMA &&
            strongMomentum &&
            ultraVolume &&
            indicators.rsi > 70) ||
          (gapDown && explosiveRedCandle && volumeBreakout)
        );

      case "MEDIUM_VOLATILITY":
        // June 2025 style - Balanced approach with more signals
        return (
          (explosiveRedCandle &&
            volumeSpike &&
            bearishTrend &&
            indicators.rsi > 60) ||
          (rsiWeakening && bearishEngulfing && ultraVolume && emaGap) ||
          (breakdownBelowSMA && strongMomentum && volumeSpike) ||
          (shootingStarPattern && indicators.rsi > 75 && volumeSpike) ||
          (strongBearishTrend &&
            explosiveRedCandle &&
            volumeSpike &&
            indicators.rsi > 50)
        );

      case "LOW_VOLATILITY":
        // Sep-Dec 2024 style - More frequent, smaller opportunities
        return (
          (explosiveRedCandle && volumeSpike && bearishTrend) ||
          (rsiWeakening && bearishTrend && strongMomentum && volumeSpike) ||
          (breakdownBelowSMA && strongMomentum && volumeSpike) ||
          (bearishEngulfing && bearishTrend && volumeSpike) ||
          (priceAcceleration &&
            explosiveRedCandle &&
            volumeSpike &&
            strongBearishTrend) ||
          (shootingStarPattern && indicators.rsi > 70 && volumeSpike)
        );

      default:
        return false;
    }
  }

  private shouldExitPosition(
    candle: CandleData,
    indicators: any,
    regime: MarketRegime
  ): boolean {
    if (!this.position.side) return false;

    const currentPrice = candle.close;
    const entryPrice = this.position.entryPrice;

    // Update highest/lowest prices for trailing stop
    if (this.position.side === "LONG") {
      if (
        !this.position.highestPrice ||
        currentPrice > this.position.highestPrice
      ) {
        this.position.highestPrice = currentPrice;
      }
    } else {
      if (
        !this.position.lowestPrice ||
        currentPrice < this.position.lowestPrice
      ) {
        this.position.lowestPrice = currentPrice;
      }
    }

    const pnlPercent =
      this.position.side === "LONG"
        ? (currentPrice - entryPrice) / entryPrice
        : (entryPrice - currentPrice) / entryPrice;

    // Hard stop loss
    if (pnlPercent <= -this.STOP_LOSS_PERCENT) {
      return true;
    }

    // Take profit at target
    if (pnlPercent >= this.TAKE_PROFIT_PERCENT) {
      return true;
    }

    // Adaptive trailing stop based on regime
    let trailingActivation = 0.03; // Default 3%
    let trailingStopPercent = 0.025; // Default 2.5%

    switch (regime.regime) {
      case "HIGH_VOLATILITY":
        trailingActivation = 0.05; // 5% activation
        trailingStopPercent = 0.03; // 3% trailing
        break;
      case "MEDIUM_VOLATILITY":
        trailingActivation = 0.03; // 3% activation
        trailingStopPercent = 0.025; // 2.5% trailing
        break;
      case "LOW_VOLATILITY":
        trailingActivation = 0.02; // 2% activation
        trailingStopPercent = 0.015; // 1.5% trailing
        break;
    }

    if (pnlPercent > trailingActivation) {
      if (this.position.side === "LONG" && this.position.highestPrice) {
        const trailingStopPrice =
          this.position.highestPrice * (1 - trailingStopPercent);
        if (currentPrice <= trailingStopPrice) {
          return true;
        }
      }

      if (this.position.side === "SHORT" && this.position.lowestPrice) {
        const trailingStopPrice =
          this.position.lowestPrice * (1 + trailingStopPercent);
        if (currentPrice >= trailingStopPrice) {
          return true;
        }
      }
    }

    // Adaptive RSI reversal signals based on regime
    let rsiExitLong = 85;
    let rsiExitShort = 15;

    switch (regime.regime) {
      case "HIGH_VOLATILITY":
        rsiExitLong = 90;
        rsiExitShort = 10;
        break;
      case "MEDIUM_VOLATILITY":
        rsiExitLong = 85;
        rsiExitShort = 15;
        break;
      case "LOW_VOLATILITY":
        rsiExitLong = 75;
        rsiExitShort = 25;
        break;
    }

    if (this.position.side === "LONG" && indicators.rsi > rsiExitLong) {
      return true;
    }

    if (this.position.side === "SHORT" && indicators.rsi < rsiExitShort) {
      return true;
    }

    return false;
  }

  private calculatePositionSize(regime: MarketRegime): number {
    // ADAPTIVE POSITION SIZING based on market regime
    let riskPercent = this.RISK_PER_TRADE;

    // Dynamic risk scaling for maximum compound growth
    const balanceMultiplier = this.balance / this.initialBalance;

    if (balanceMultiplier > 7) {
      riskPercent *= 0.7; // Reduce risk when very ahead
    } else if (balanceMultiplier > 5) {
      riskPercent *= 0.8; // Moderate reduction when ahead
    } else if (balanceMultiplier > 3) {
      riskPercent *= 0.9; // Slight reduction when ahead
    } else if (balanceMultiplier > 2) {
      riskPercent *= 1.0; // Normal risk when moderately ahead
    } else if (balanceMultiplier > 1.5) {
      riskPercent *= 1.1; // Increase risk when slightly ahead
    } else {
      riskPercent *= 1.2; // Maximum aggression when behind
    }

    // Regime-based risk adjustment
    switch (regime.regime) {
      case "HIGH_VOLATILITY":
        riskPercent *= 1.2; // 20% more aggressive in high vol
        break;
      case "MEDIUM_VOLATILITY":
        riskPercent *= 1.0; // Normal risk
        break;
      case "LOW_VOLATILITY":
        riskPercent *= 0.8; // 20% more conservative in low vol
        break;
    }

    const currentRiskAmount = this.balance * Math.min(riskPercent, 0.6); // Max 60% risk
    const maxRiskAmount = this.balance * 0.7; // Max 70% of current balance

    const riskAmount = Math.min(currentRiskAmount, maxRiskAmount);
    return riskAmount * this.LEVERAGE;
  }

  private openPosition(
    side: "LONG" | "SHORT",
    price: number,
    time: string,
    regime: MarketRegime
  ): void {
    const size = this.calculatePositionSize(regime);

    this.position = {
      side,
      size,
      entryPrice: price,
      entryTime: time,
      leverage: this.LEVERAGE,
      highestPrice: side === "LONG" ? price : undefined,
      lowestPrice: side === "SHORT" ? price : undefined,
    };

    console.log(
      `[${
        regime.regime
      }] Opened ${side} position at ${price} with size ${size.toFixed(
        4
      )} (${time})`
    );
  }

  private closePosition(price: number, time: string): void {
    if (!this.position.side) return;

    const priceChangePercent =
      this.position.side === "LONG"
        ? (price - this.position.entryPrice) / this.position.entryPrice
        : (this.position.entryPrice - price) / this.position.entryPrice;

    const positionValue = this.position.size / this.LEVERAGE;
    const pnl = positionValue * priceChangePercent * this.LEVERAGE;
    const pnlPercent = priceChangePercent * this.LEVERAGE * 100;

    // Cap maximum loss to prevent account blowup
    const maxLoss = this.balance * 0.05; // Max 5% loss per trade
    const cappedPnl = Math.max(pnl, -maxLoss);

    this.balance += cappedPnl;

    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }

    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade: Trade = {
      entryTime: this.position.entryTime,
      exitTime: time,
      side: this.position.side,
      entryPrice: this.position.entryPrice,
      exitPrice: price,
      size: this.position.size,
      pnl: cappedPnl,
      pnlPercent,
      leverage: this.position.leverage,
    };

    this.trades.push(trade);

    console.log(
      `Closed ${
        this.position.side
      } position at ${price}. PnL: ${cappedPnl.toFixed(
        2
      )} USDT (${pnlPercent.toFixed(2)}%). Balance: ${this.balance.toFixed(
        2
      )} USDT`
    );

    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      highestPrice: undefined,
      lowestPrice: undefined,
    };
  }

  async runBacktest(datasetName: string): Promise<BacktestResult> {
    console.log(
      `\n🚀 Starting ADAPTIVE ULTIMATE STRATEGY backtest on ${datasetName}...`
    );
    console.log(`Initial balance: ${this.initialBalance} USDT`);
    console.log(`Target: ${this.initialBalance * 8} USDT (8x return)`);

    let regimeChanges = 0;
    let lastRegime = "MEDIUM_VOLATILITY";

    for (let i = 1; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      if (!indicators) continue;

      // Calculate and adapt to market regime
      const currentRegime = this.calculateMarketRegime(i);
      if (currentRegime.regime !== lastRegime) {
        regimeChanges++;
        console.log(
          `📊 Market regime changed to ${currentRegime.regime} at ${candle.time}`
        );
        lastRegime = currentRegime.regime;
      }

      this.adaptParametersToMarket(currentRegime);
      this.marketRegime = currentRegime;

      // Check for exit signals if position exists
      if (
        this.position.side &&
        this.shouldExitPosition(candle, indicators, currentRegime)
      ) {
        this.closePosition(candle.close, candle.time);
      }

      // Check for entry signals if no position
      if (!this.position.side) {
        if (
          this.shouldEnterLong(candle, indicators, prevCandle, currentRegime)
        ) {
          this.openPosition("LONG", candle.close, candle.time, currentRegime);
        } else if (
          this.shouldEnterShort(candle, indicators, prevCandle, currentRegime)
        ) {
          this.openPosition("SHORT", candle.close, candle.time, currentRegime);
        }
      }

      // Emergency exit if balance drops too low
      if (this.balance < this.initialBalance * 0.05) {
        console.log("⚠️ Emergency exit: Balance dropped below 5% of initial");
        if (this.position.side) {
          this.closePosition(candle.close, candle.time);
        }
        break;
      }
    }

    // Close any remaining position
    if (this.position.side) {
      const lastCandle = this.data[this.data.length - 1];
      this.closePosition(lastCandle.close, lastCandle.time);
    }

    const winningTrades = this.trades.filter((t) => t.pnl > 0);
    const losingTrades = this.trades.filter((t) => t.pnl <= 0);
    const totalReturn = this.balance - this.initialBalance;
    const totalReturnPercent = (totalReturn / this.initialBalance) * 100;

    // Calculate advanced metrics
    const avgWin =
      winningTrades.length > 0
        ? winningTrades.reduce((sum, t) => sum + t.pnl, 0) /
          winningTrades.length
        : 0;
    const avgLoss =
      losingTrades.length > 0
        ? Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0)) /
          losingTrades.length
        : 0;
    const profitFactor =
      avgLoss > 0
        ? (avgWin * winningTrades.length) / (avgLoss * losingTrades.length)
        : 0;

    // Calculate consecutive wins/losses
    let maxConsecutiveWins = 0;
    let maxConsecutiveLosses = 0;
    let currentWins = 0;
    let currentLosses = 0;

    for (const trade of this.trades) {
      if (trade.pnl > 0) {
        currentWins++;
        currentLosses = 0;
        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentWins);
      } else {
        currentLosses++;
        currentWins = 0;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentLosses);
      }
    }

    // Calculate Sharpe ratio (simplified)
    const returns = this.trades.map((t) => t.pnlPercent / 100);
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    const returnStdDev = Math.sqrt(
      returns.reduce((a, b) => a + Math.pow(b - avgReturn, 2), 0) /
        returns.length
    );
    const sharpeRatio = returnStdDev > 0 ? avgReturn / returnStdDev : 0;

    console.log(`📊 Regime changes detected: ${regimeChanges}`);

    return {
      dataset: datasetName,
      initialBalance: this.initialBalance,
      finalBalance: this.balance,
      totalReturn,
      totalReturnPercent,
      totalTrades: this.trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate:
        this.trades.length > 0
          ? (winningTrades.length / this.trades.length) * 100
          : 0,
      maxDrawdown: this.maxDrawdown * 100,
      sharpeRatio,
      trades: this.trades,
      avgWin,
      avgLoss,
      profitFactor,
      maxConsecutiveLosses,
      maxConsecutiveWins,
    };
  }

  async compareAdaptiveStrategy(): Promise<void> {
    const datasets = [
      {
        name: "Sep-Dec 2024",
        path: "./data/historical_data_9_12_2024_15m.csv",
      },
      { name: "March 2025", path: "./data/historical_data_mar_15m.csv" },
      { name: "June 2025", path: "./data/historical_data_jun_15m.csv" },
    ];

    const results: BacktestResult[] = [];

    for (const dataset of datasets) {
      console.log(`\n${"=".repeat(70)}`);
      console.log(`🎯 ADAPTIVE STRATEGY TESTING: ${dataset.name}`);
      console.log(`${"=".repeat(70)}`);

      this.resetForNewDataset();
      await this.loadData(dataset.path);
      const result = await this.runBacktest(dataset.name);
      results.push(result);

      console.log(`\n🎯 ${dataset.name} ADAPTIVE Results:`);
      console.log(`Final Balance: ${result.finalBalance.toFixed(2)} USDT`);
      console.log(
        `Total Return: ${result.totalReturn.toFixed(
          2
        )} USDT (${result.totalReturnPercent.toFixed(2)}%)`
      );
      console.log(`Total Trades: ${result.totalTrades}`);
      console.log(`Win Rate: ${result.winRate.toFixed(2)}%`);
      console.log(`Max Drawdown: ${result.maxDrawdown.toFixed(2)}%`);
      console.log(`Profit Factor: ${result.profitFactor.toFixed(2)}`);
      console.log(
        `Progress to 8x Target: ${(
          result.finalBalance / this.initialBalance
        ).toFixed(2)}x`
      );

      if (result.finalBalance >= this.initialBalance * 8) {
        console.log("🎉 TARGET ACHIEVED! 8x return reached!");
      } else {
        const progressPercent = (
          (result.finalBalance / this.initialBalance / 8) *
          100
        ).toFixed(1);
        console.log(`📊 Progress: ${progressPercent}% of 8x target`);
      }
    }

    // Comprehensive analysis
    console.log(`\n${"=".repeat(100)}`);
    console.log(`🏆 ADAPTIVE STRATEGY COMPREHENSIVE ANALYSIS`);
    console.log(`${"=".repeat(100)}`);

    const [sepDecResult, marchResult, juneResult] = results;

    console.log(`\n📊 ADAPTIVE STRATEGY PERFORMANCE TABLE:`);
    console.log(
      `┌─────────────────────────┬─────────────────┬─────────────────┬─────────────────┐`
    );
    console.log(
      `│ Metric                  │ Sep-Dec 2024    │ March 2025      │ June 2025       │`
    );
    console.log(
      `├─────────────────────────┼─────────────────┼─────────────────┼─────────────────┤`
    );
    console.log(
      `│ Final Balance (USDT)    │ ${sepDecResult.finalBalance
        .toFixed(2)
        .padStart(15)} │ ${marchResult.finalBalance
        .toFixed(2)
        .padStart(15)} │ ${juneResult.finalBalance.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Return (%)              │ ${sepDecResult.totalReturnPercent
        .toFixed(2)
        .padStart(15)} │ ${marchResult.totalReturnPercent
        .toFixed(2)
        .padStart(15)} │ ${juneResult.totalReturnPercent
        .toFixed(2)
        .padStart(15)} │`
    );
    console.log(
      `│ Progress to 8x Target   │ ${(
        sepDecResult.finalBalance / this.initialBalance
      )
        .toFixed(2)
        .padStart(15)} │ ${(marchResult.finalBalance / this.initialBalance)
        .toFixed(2)
        .padStart(15)} │ ${(juneResult.finalBalance / this.initialBalance)
        .toFixed(2)
        .padStart(15)} │`
    );
    console.log(
      `│ Total Trades            │ ${sepDecResult.totalTrades
        .toString()
        .padStart(15)} │ ${marchResult.totalTrades
        .toString()
        .padStart(15)} │ ${juneResult.totalTrades.toString().padStart(15)} │`
    );
    console.log(
      `│ Win Rate (%)            │ ${sepDecResult.winRate
        .toFixed(2)
        .padStart(15)} │ ${marchResult.winRate
        .toFixed(2)
        .padStart(15)} │ ${juneResult.winRate.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Profit Factor           │ ${sepDecResult.profitFactor
        .toFixed(2)
        .padStart(15)} │ ${marchResult.profitFactor
        .toFixed(2)
        .padStart(15)} │ ${juneResult.profitFactor.toFixed(2).padStart(15)} │`
    );
    console.log(
      `│ Max Drawdown (%)        │ ${sepDecResult.maxDrawdown
        .toFixed(2)
        .padStart(15)} │ ${marchResult.maxDrawdown
        .toFixed(2)
        .padStart(15)} │ ${juneResult.maxDrawdown.toFixed(2).padStart(15)} │`
    );
    console.log(
      `└─────────────────────────┴─────────────────┴─────────────────┴─────────────────┘`
    );

    // Success analysis
    const successfulDatasets = results.filter(
      (r) => r.finalBalance >= this.initialBalance * 8
    );
    const avgReturn =
      results.reduce((sum, r) => sum + r.totalReturnPercent, 0) /
      results.length;
    const avgProgress =
      results.reduce(
        (sum, r) => sum + r.finalBalance / this.initialBalance,
        0
      ) / results.length;

    console.log(`\n🎯 ADAPTIVE STRATEGY SUCCESS ANALYSIS:`);
    console.log(
      `✅ Datasets achieving 8x target: ${successfulDatasets.length}/3`
    );
    console.log(
      `📊 Average return across all datasets: ${avgReturn.toFixed(2)}%`
    );
    console.log(`📈 Average progress to 8x target: ${avgProgress.toFixed(2)}x`);

    if (successfulDatasets.length === 3) {
      console.log(`🎉 PERFECT SUCCESS! All datasets achieved 8x target!`);
    } else if (successfulDatasets.length >= 2) {
      console.log(
        `🚀 EXCELLENT! ${successfulDatasets.length} out of 3 datasets achieved 8x target!`
      );
    } else if (successfulDatasets.length === 1) {
      console.log(
        `📈 GOOD! 1 dataset achieved 8x target. Strategy shows potential.`
      );
    } else {
      console.log(
        `📊 Strategy needs further optimization to consistently achieve 8x target.`
      );
    }

    // Save results
    const comparisonData = {
      adaptiveStrategy: {
        sepDecResult,
        marchResult,
        juneResult,
        successfulDatasets: successfulDatasets.length,
        averageReturn: avgReturn,
        averageProgress: avgProgress,
        allTargetsAchieved: successfulDatasets.length === 3,
      },
    };

    fs.writeFileSync(
      "./adaptive_strategy_results.json",
      JSON.stringify(comparisonData, null, 2)
    );
    console.log(
      "\n📁 Adaptive strategy results saved to adaptive_strategy_results.json"
    );
  }
}

// Main execution
async function main() {
  const adaptiveStrategy = new AdaptiveUltimateStrategy(100);

  try {
    await adaptiveStrategy.compareAdaptiveStrategy();
  } catch (error) {
    console.error("Error running adaptive strategy:", error);
  }
}

// Run the adaptive strategy
main();
