{"name": "ultimate-8x-trading-strategy", "version": "2.0.0", "main": "ultimate_8x_strategy.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "lint:fix": "eslint --fix .", "run": "node --loader ts-node/esm real_time_bot.ts", "bot": "node --loader ts-node/esm real_time_bot.ts", "demo": "node --loader ts-node/esm demo_bot.ts", "test-config": "node --loader ts-node/esm test_config.ts", "backtest": "node --loader ts-node/esm back_test_pet.ts", "backtest-optimized": "node --loader ts-node/esm back_test_optimized.ts", "compare-datasets": "node --loader ts-node/esm compare_datasets.ts", "adaptive-strategy": "node --loader ts-node/esm adaptive_strategy.ts", "ultimate-8x": "node --loader ts-node/esm ultimate_8x_strategy.ts", "ultimate-20x": "node --loader ts-node/esm ultimate_20x_strategy.ts", "strategy-8x": "node --loader ts-node/esm run_strategy.ts 8x", "strategy-20x": "node --loader ts-node/esm run_strategy.ts 20x", "strategy-compare": "node --loader ts-node/esm run_strategy.ts compare", "strategy-all": "node --loader ts-node/esm run_strategy.ts all", "test-strategies": "node --loader ts-node/esm test_strategies.ts", "simple-test": "node --loader ts-node/esm simple_strategy_test.ts", "ultra-20x": "node --loader ts-node/esm ultra_20x_test.ts", "smart-20x": "node --loader ts-node/esm smart_20x_strategy.ts", "target-20x": "node --loader ts-node/esm target_20x_strategy.ts", "analyze-patterns": "node --loader ts-node/esm pattern_analyzer.ts", "analyze-comprehensive": "node --loader ts-node/esm comprehensive_pattern_analysis.ts", "data-driven": "node --loader ts-node/esm data_driven_strategy.ts", "data-based": "node --loader ts-node/esm data_based_strategy.ts", "pump-predictor": "node --loader ts-node/esm pump_predictor.ts", "pump-backtest": "node --loader ts-node/esm pump_backtest.ts", "show-options": "node --loader ts-node/esm show_options.ts", "help": "npm run show-options", "test-new-data": "node --loader ts-node/esm test_new_data.ts", "start": "npm run analyze-patterns", "quick-test": "npm run strategy-8x", "full-analysis": "npm run strategy-all", "validate": "npm run test-strategies"}, "type": "module", "keywords": ["trading", "algorithm", "cryptocurrency", "bitcoin", "strategy", "8x-returns", "adaptive", "market-regime", "risk-management", "backtesting"], "author": "Ultimate 8X Strategy Team", "license": "MIT", "description": "Adaptive algorithmic trading strategy achieving consistent 8x returns across diverse market conditions", "dependencies": {"binance-api-node": "file:binance-api-node", "csv-parser": "^3.1.0", "dotenv": "^16.4.7", "fs": "^0.0.1-security", "set-interval-async": "^3.0.3", "technicalindicators": "^3.1.0", "ts-node": "^10.9.2"}, "devDependencies": {"@babel/core": "^7.26.9", "@babel/plugin-transform-runtime": "^7.26.9", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@tsconfig/node20": "^20.1.4", "eslint": "^9.20.1"}}