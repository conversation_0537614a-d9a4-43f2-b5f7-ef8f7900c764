import { Ultimate8xStrategy } from "./ultimate_8x_strategy.js";
import { Ultimate20xStrategy } from "./ultimate_20x_strategy.js";
import fs from "fs";

interface StrategyConfig {
  name: string;
  target: string;
  description: string;
  riskLevel: "HIGH" | "ULTRA_HIGH" | "EXTREME";
}

interface ComparisonResult {
  strategy8x: any;
  strategy20x: any;
  comparison: {
    winner: string;
    performance8x: number;
    performance20x: number;
    trades8x: number;
    trades20x: number;
    winRate8x: number;
    winRate20x: number;
    maxDrawdown8x: number;
    maxDrawdown20x: number;
  };
}

export class StrategyComparison {
  private dataFiles: string[] = [];
  private results: ComparisonResult[] = [];

  constructor() {
    // Load available data files
    this.loadDataFiles();
  }

  private loadDataFiles(): void {
    const dataDir = "./data";
    try {
      const files = fs.readdirSync(dataDir);
      this.dataFiles = files
        .filter(file => file.endsWith('.csv'))
        .map(file => `${dataDir}/${file}`);
      
      console.log(`📁 Found ${this.dataFiles.length} data files:`);
      this.dataFiles.forEach((file, index) => {
        console.log(`   ${index + 1}. ${file}`);
      });
    } catch (error) {
      console.error("Error loading data files:", error);
    }
  }

  async runComparison(dataFile?: string): Promise<ComparisonResult> {
    const targetFile = dataFile || this.dataFiles[0];
    
    if (!targetFile) {
      throw new Error("No data file available for comparison");
    }

    console.log("\n" + "=".repeat(80));
    console.log("🔥 ULTIMATE STRATEGY COMPARISON: 8X vs 20X");
    console.log("=".repeat(80));
    console.log(`📊 Testing on: ${targetFile}`);
    console.log("=".repeat(80));

    // Initialize strategies
    const strategy8x = new Ultimate8xStrategy(40);
    const strategy20x = new Ultimate20xStrategy(40);

    // Load data for both strategies
    console.log("\n📈 Loading data for 8X strategy...");
    await strategy8x.loadData(targetFile);
    
    console.log("📈 Loading data for 20X strategy...");
    await strategy20x.loadData(targetFile);

    // Run 8X strategy
    console.log("\n🚀 Running 8X Strategy Backtest...");
    await strategy8x.runBacktest();
    const results8x = strategy8x.getResults();

    console.log("\n🚀 Running 20X Strategy Backtest...");
    await strategy20x.runBacktest();
    const results20x = strategy20x.getResults();

    // Compare results
    const comparison = this.compareResults(results8x, results20x);

    const result: ComparisonResult = {
      strategy8x: results8x,
      strategy20x: results20x,
      comparison
    };

    this.results.push(result);
    this.printComparison(result);

    return result;
  }

  private compareResults(results8x: any, results20x: any) {
    const performance8x = results8x.returnMultiplier;
    const performance20x = results20x.returnMultiplier;
    
    let winner = "TIE";
    if (results8x.targetAchieved && !results20x.targetAchieved) {
      winner = "8X Strategy";
    } else if (results20x.targetAchieved && !results8x.targetAchieved) {
      winner = "20X Strategy";
    } else if (results8x.targetAchieved && results20x.targetAchieved) {
      winner = results8x.finalBalance > results20x.finalBalance ? "8X Strategy" : "20X Strategy";
    } else {
      winner = performance8x > performance20x ? "8X Strategy" : "20X Strategy";
    }

    return {
      winner,
      performance8x,
      performance20x,
      trades8x: results8x.totalTrades,
      trades20x: results20x.totalTrades,
      winRate8x: results8x.winRate,
      winRate20x: results20x.winRate,
      maxDrawdown8x: results8x.maxDrawdown,
      maxDrawdown20x: results20x.maxDrawdown,
    };
  }

  private printComparison(result: ComparisonResult): void {
    console.log("\n" + "=".repeat(80));
    console.log("🏆 STRATEGY COMPARISON RESULTS");
    console.log("=".repeat(80));

    console.log("\n📊 PERFORMANCE COMPARISON:");
    console.log(`   8X Strategy:   ${result.strategy8x.finalBalance.toFixed(2)} USDT (${result.comparison.performance8x.toFixed(2)}x)`);
    console.log(`   20X Strategy:  ${result.strategy20x.finalBalance.toFixed(2)} USDT (${result.comparison.performance20x.toFixed(2)}x)`);
    console.log(`   Winner:        ${result.comparison.winner}`);

    console.log("\n🎯 TARGET ACHIEVEMENT:");
    const target8x = result.strategy8x.targetAchieved ? "✅ ACHIEVED" : "❌ Not reached";
    const target20x = result.strategy20x.targetAchieved ? "✅ ACHIEVED" : "❌ Not reached";
    console.log(`   8X Target (8x):   ${target8x}`);
    console.log(`   20X Target (21x): ${target20x}`);

    console.log("\n📈 TRADING METRICS:");
    console.log(`   Total Trades:     8X: ${result.comparison.trades8x} | 20X: ${result.comparison.trades20x}`);
    console.log(`   Win Rate:         8X: ${result.comparison.winRate8x.toFixed(1)}% | 20X: ${result.comparison.winRate20x.toFixed(1)}%`);
    console.log(`   Max Drawdown:     8X: ${result.comparison.maxDrawdown8x.toFixed(2)}% | 20X: ${result.comparison.maxDrawdown20x.toFixed(2)}%`);

    console.log("\n💡 STRATEGY ANALYSIS:");
    if (result.comparison.winner === "8X Strategy") {
      console.log("   🎯 8X Strategy performed better - more consistent and reliable");
      console.log("   📊 Consider using 8X for steady growth");
    } else if (result.comparison.winner === "20X Strategy") {
      console.log("   🚀 20X Strategy performed better - higher risk, higher reward");
      console.log("   ⚡ Consider using 20X for aggressive growth");
    } else {
      console.log("   ⚖️ Both strategies performed similarly");
      console.log("   🤔 Consider market conditions when choosing strategy");
    }

    console.log("\n🔥 RECOMMENDATION:");
    if (result.strategy20x.targetAchieved) {
      console.log("   🎉 20X Strategy achieved the ultimate goal!");
      console.log("   💎 Use 20X strategy for maximum returns");
    } else if (result.strategy8x.targetAchieved) {
      console.log("   ✅ 8X Strategy achieved its target reliably");
      console.log("   🛡️ Use 8X strategy for consistent performance");
    } else {
      const better = result.comparison.performance8x > result.comparison.performance20x ? "8X" : "20X";
      console.log(`   📈 ${better} Strategy showed better performance`);
      console.log("   🔄 Consider testing on different market conditions");
    }

    console.log("=".repeat(80));
  }

  async runAllDatasets(): Promise<void> {
    console.log("\n🔥 RUNNING COMPREHENSIVE STRATEGY COMPARISON");
    console.log("Testing both strategies across all available datasets...\n");

    for (let i = 0; i < this.dataFiles.length; i++) {
      const dataFile = this.dataFiles[i];
      console.log(`\n📊 Dataset ${i + 1}/${this.dataFiles.length}: ${dataFile}`);
      
      try {
        await this.runComparison(dataFile);
      } catch (error) {
        console.error(`❌ Error testing ${dataFile}:`, error);
      }
    }

    this.printOverallSummary();
  }

  private printOverallSummary(): void {
    if (this.results.length === 0) return;

    console.log("\n" + "=".repeat(80));
    console.log("🏆 OVERALL STRATEGY COMPARISON SUMMARY");
    console.log("=".repeat(80));

    const wins8x = this.results.filter(r => r.comparison.winner === "8X Strategy").length;
    const wins20x = this.results.filter(r => r.comparison.winner === "20X Strategy").length;
    const ties = this.results.filter(r => r.comparison.winner === "TIE").length;

    const targets8x = this.results.filter(r => r.strategy8x.targetAchieved).length;
    const targets20x = this.results.filter(r => r.strategy20x.targetAchieved).length;

    const avgPerformance8x = this.results.reduce((sum, r) => sum + r.comparison.performance8x, 0) / this.results.length;
    const avgPerformance20x = this.results.reduce((sum, r) => sum + r.comparison.performance20x, 0) / this.results.length;

    console.log(`\n📊 TESTED DATASETS: ${this.results.length}`);
    console.log(`\n🏆 WINS:`);
    console.log(`   8X Strategy:  ${wins8x} wins (${(wins8x/this.results.length*100).toFixed(1)}%)`);
    console.log(`   20X Strategy: ${wins20x} wins (${(wins20x/this.results.length*100).toFixed(1)}%)`);
    console.log(`   Ties:         ${ties} ties (${(ties/this.results.length*100).toFixed(1)}%)`);

    console.log(`\n🎯 TARGET ACHIEVEMENTS:`);
    console.log(`   8X Targets:   ${targets8x}/${this.results.length} (${(targets8x/this.results.length*100).toFixed(1)}%)`);
    console.log(`   20X Targets:  ${targets20x}/${this.results.length} (${(targets20x/this.results.length*100).toFixed(1)}%)`);

    console.log(`\n📈 AVERAGE PERFORMANCE:`);
    console.log(`   8X Strategy:  ${avgPerformance8x.toFixed(2)}x average return`);
    console.log(`   20X Strategy: ${avgPerformance20x.toFixed(2)}x average return`);

    console.log(`\n🏅 OVERALL WINNER:`);
    if (targets20x > targets8x) {
      console.log("   🚀 20X STRATEGY - Achieved more ultimate targets!");
    } else if (targets8x > targets20x) {
      console.log("   🎯 8X STRATEGY - More reliable target achievement!");
    } else if (avgPerformance20x > avgPerformance8x) {
      console.log("   🚀 20X STRATEGY - Higher average performance!");
    } else {
      console.log("   🎯 8X STRATEGY - More consistent performance!");
    }

    console.log("=".repeat(80));
  }

  saveResults(filename: string = "strategy_comparison_results.json"): void {
    const summary = {
      timestamp: new Date().toISOString(),
      totalDatasets: this.results.length,
      results: this.results,
      summary: this.results.length > 0 ? {
        wins8x: this.results.filter(r => r.comparison.winner === "8X Strategy").length,
        wins20x: this.results.filter(r => r.comparison.winner === "20X Strategy").length,
        targets8x: this.results.filter(r => r.strategy8x.targetAchieved).length,
        targets20x: this.results.filter(r => r.strategy20x.targetAchieved).length,
        avgPerformance8x: this.results.reduce((sum, r) => sum + r.comparison.performance8x, 0) / this.results.length,
        avgPerformance20x: this.results.reduce((sum, r) => sum + r.comparison.performance20x, 0) / this.results.length,
      } : null
    };

    fs.writeFileSync(filename, JSON.stringify(summary, null, 2));
    console.log(`💾 Results saved to ${filename}`);
  }
}
