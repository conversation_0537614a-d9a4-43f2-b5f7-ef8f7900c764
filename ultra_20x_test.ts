#!/usr/bin/env node

import fs from "fs";
import csv from "csv-parser";
import { RSI, EMA, SMA } from "technicalindicators";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  entryReason: string;
}

interface Trade {
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  entryTime: string;
  exitTime: string;
  size: number;
  pnl: number;
  pnlPercent: number;
  reason: string;
  entryReason: string;
  leverage: number;
  balanceBefore: number;
  balanceAfter: number;
}

class Ultra20xTest {
  private data: CandleData[] = [];
  private balance: number = 40;
  private initialBalance: number = 40;
  private trades: Trade[] = [];

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateIndicators(index: number): any {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);
    
    const closes = candles.map(c => c.close);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 8, values: closes });
    const emaSlowValues = EMA.calculate({ period: 21, values: closes });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast: emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
    };
  }

  private resetStrategy(): void {
    this.balance = this.initialBalance;
    this.trades = [];
  }

  async testUltra20xStrategy(dataFile: string): Promise<any> {
    console.log(`\n🚀 Testing ULTRA 20X Strategy on: ${dataFile}`);
    console.log("=" .repeat(60));
    
    this.resetStrategy();
    await this.loadData(dataFile);
    
    let position: Position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "" };
    let maxBalance = this.balance;
    let maxDrawdown = 0;
    let targetAchieved = false;

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const indicators = this.calculateIndicators(i);
      
      // ULTRA AGGRESSIVE ENTRY CONDITIONS
      if (!position.side) {
        const volumeAvg = this.data.slice(Math.max(0, i - 10), i).reduce((sum, c) => sum + c.volume, 0) / 10;
        const isHighVolume = candle.volume > volumeAvg * 1.1;
        const priceChange = Math.abs((candle.close - candle.open) / candle.open);
        const isVolatile = priceChange > 0.003;
        
        // MAXIMUM AGGRESSION ENTRY CONDITIONS
        const shouldEnterLong = 
          (indicators.rsi < 50 && candle.close > indicators.emaFast) ||
          (indicators.rsi < 55 && isHighVolume && isVolatile && candle.close > candle.open) ||
          (candle.close > indicators.emaFast * 1.001 && indicators.emaFast > indicators.emaSlow);
          
        const shouldEnterShort = 
          (indicators.rsi > 50 && candle.close < indicators.emaFast) ||
          (indicators.rsi > 45 && isHighVolume && isVolatile && candle.close < candle.open) ||
          (candle.close < indicators.emaFast * 0.999 && indicators.emaFast < indicators.emaSlow);

        if (shouldEnterLong || shouldEnterShort) {
          // EXTREME AGGRESSION: 95% risk, up to 200x leverage
          const balanceMultiplier = this.balance / this.initialBalance;
          let riskPercent = 0.95;
          let leverage = 150;
          
          // Scale up aggression as we grow
          if (balanceMultiplier >= 10) {
            riskPercent = 0.98;
            leverage = 200;
          } else if (balanceMultiplier >= 5) {
            riskPercent = 0.97;
            leverage = 175;
          } else if (balanceMultiplier >= 2) {
            riskPercent = 0.96;
            leverage = 160;
          }
          
          const size = this.balance * riskPercent * leverage;
          const side = shouldEnterLong ? "LONG" : "SHORT";
          
          position = {
            side,
            size,
            entryPrice: candle.close,
            entryTime: candle.time,
            leverage,
            entryReason: "ULTRA_AGGRESSIVE_ENTRY",
          };
          
          console.log(`💥 ULTRA ${side} at ${candle.close} | RSI: ${indicators.rsi.toFixed(1)} | Risk: ${(riskPercent*100).toFixed(0)}% | Leverage: ${leverage}x | Balance: ${this.balance.toFixed(2)}`);
        }
      } else {
        // ULTRA AGGRESSIVE EXIT CONDITIONS
        const pnlPercent = position.side === "LONG" 
          ? (candle.close - position.entryPrice) / position.entryPrice
          : (position.entryPrice - candle.close) / position.entryPrice;

        let shouldExit = false;
        let exitReason = "";
        
        // Dynamic take profit based on balance growth
        const balanceMultiplier = this.balance / this.initialBalance;
        let takeProfitTarget = 1.5; // Base 150% take profit
        
        if (balanceMultiplier >= 15) {
          takeProfitTarget = 2.0; // 200% when 15x ahead
        } else if (balanceMultiplier >= 10) {
          takeProfitTarget = 1.8; // 180% when 10x ahead
        } else if (balanceMultiplier >= 5) {
          takeProfitTarget = 1.6; // 160% when 5x ahead
        }
        
        // MASSIVE TAKE PROFIT
        if (pnlPercent >= takeProfitTarget) {
          shouldExit = true;
          exitReason = `ULTRA_PROFIT_${(takeProfitTarget*100).toFixed(0)}%`;
        }
        // ULTRA TIGHT STOP LOSS
        else if (pnlPercent <= -0.001) {
          shouldExit = true;
          exitReason = "ULTRA_STOP";
        }
        // Quick profit at 50%
        else if (pnlPercent >= 0.5 && Math.random() < 0.2) {
          shouldExit = true;
          exitReason = "QUICK_PROFIT_50%";
        }
        // RSI reversal
        else if (position.side === "LONG" && indicators.rsi > 85) {
          shouldExit = true;
          exitReason = "RSI_REVERSAL";
        } else if (position.side === "SHORT" && indicators.rsi < 15) {
          shouldExit = true;
          exitReason = "RSI_REVERSAL";
        }

        if (shouldExit) {
          const pnl = position.side === "LONG" 
            ? (candle.close - position.entryPrice) * position.size / position.entryPrice
            : (position.entryPrice - candle.close) * position.size / position.entryPrice;

          const balanceBefore = this.balance;
          this.balance += pnl;

          if (this.balance > maxBalance) maxBalance = this.balance;
          const currentDrawdown = (maxBalance - this.balance) / maxBalance;
          if (currentDrawdown > maxDrawdown) maxDrawdown = currentDrawdown;

          this.trades.push({
            side: position.side,
            entryPrice: position.entryPrice,
            exitPrice: candle.close,
            entryTime: position.entryTime,
            exitTime: candle.time,
            size: position.size,
            pnl,
            pnlPercent,
            reason: exitReason,
            entryReason: position.entryReason,
            leverage: position.leverage,
            balanceBefore,
            balanceAfter: this.balance
          });

          const pnlColor = pnl > 0 ? "🟢" : "🔴";
          const currentMultiplier = this.balance / this.initialBalance;
          console.log(`${pnlColor} EXIT ${position.side} | PnL: ${pnl.toFixed(2)} (${(pnlPercent*100).toFixed(1)}%) | Balance: ${this.balance.toFixed(2)} (${currentMultiplier.toFixed(2)}x) | ${exitReason}`);

          position = { side: null, size: 0, entryPrice: 0, entryTime: "", leverage: 1, entryReason: "" };
        }
      }

      // Progress tracking
      const currentMultiplier = this.balance / this.initialBalance;
      
      if (currentMultiplier >= 5 && currentMultiplier < 5.1) {
        console.log(`🔥 5X MILESTONE! Balance: ${this.balance.toFixed(2)} USDT (${currentMultiplier.toFixed(2)}x)`);
      } else if (currentMultiplier >= 10 && currentMultiplier < 10.1) {
        console.log(`🚀 10X MILESTONE! Balance: ${this.balance.toFixed(2)} USDT (${currentMultiplier.toFixed(2)}x)`);
      } else if (currentMultiplier >= 15 && currentMultiplier < 15.1) {
        console.log(`💎 15X MILESTONE! Balance: ${this.balance.toFixed(2)} USDT (${currentMultiplier.toFixed(2)}x)`);
      } else if (currentMultiplier >= 20 && currentMultiplier < 20.1) {
        console.log(`🌟 20X MILESTONE! Balance: ${this.balance.toFixed(2)} USDT (${currentMultiplier.toFixed(2)}x)`);
      }
      
      // Check if 21x target achieved
      if (this.balance >= this.initialBalance * 21) {
        console.log(`🎉🎉🎉 21X TARGET ACHIEVED! 🎉🎉🎉`);
        console.log(`💰 Final Balance: ${this.balance.toFixed(2)} USDT (${currentMultiplier.toFixed(2)}x)`);
        console.log(`💎 Profit: ${(this.balance - this.initialBalance).toFixed(2)} USDT`);
        targetAchieved = true;
        break;
      }
      
      // Emergency exit
      if (this.balance < this.initialBalance * 0.01) {
        console.log(`⚠️ EMERGENCY EXIT: Balance too low`);
        break;
      }
    }

    const winningTrades = this.trades.filter(t => t.pnl > 0);
    const winRate = this.trades.length > 0 ? (winningTrades.length / this.trades.length) * 100 : 0;
    const returnMultiplier = this.balance / this.initialBalance;

    const result = {
      dataFile,
      targetAchieved,
      finalBalance: this.balance,
      returnMultiplier,
      totalTrades: this.trades.length,
      winRate,
      maxDrawdown: maxDrawdown * 100,
      trades: this.trades
    };

    console.log(`\n📊 RESULT: ${targetAchieved ? '🎉 SUCCESS' : '❌ FAILED'}`);
    console.log(`   Final: ${this.balance.toFixed(2)} USDT (${returnMultiplier.toFixed(2)}x)`);
    console.log(`   Trades: ${this.trades.length} | Win Rate: ${winRate.toFixed(1)}%`);
    
    return result;
  }

  async testAllDatasets(): Promise<void> {
    console.log("🚀 ULTRA 20X STRATEGY - COMPREHENSIVE TESTING");
    console.log("Testing across ALL datasets to find 21x target achievement");
    console.log("=" .repeat(80));

    const dataDir = "./data";
    let dataFiles: string[] = [];
    
    try {
      const files = fs.readdirSync(dataDir);
      dataFiles = files
        .filter(file => file.endsWith('.csv'))
        .map(file => `${dataDir}/${file}`);
    } catch (error) {
      console.error("❌ Error loading data files:", error);
      return;
    }

    const results = [];
    let successCount = 0;

    for (const dataFile of dataFiles) {
      const result = await this.testUltra20xStrategy(dataFile);
      results.push(result);
      
      if (result.targetAchieved) {
        successCount++;
      }
    }

    // Summary
    console.log("\n" + "=" .repeat(80));
    console.log("🏆 ULTRA 20X STRATEGY - FINAL SUMMARY");
    console.log("=" .repeat(80));
    
    console.log(`📊 Datasets Tested: ${dataFiles.length}`);
    console.log(`🎉 Successful (21x achieved): ${successCount}`);
    console.log(`❌ Failed: ${dataFiles.length - successCount}`);
    console.log(`📈 Success Rate: ${(successCount / dataFiles.length * 100).toFixed(1)}%`);

    // Best performance
    const bestResult = results.reduce((best, current) => 
      current.returnMultiplier > best.returnMultiplier ? current : best
    );

    console.log(`\n🏆 BEST PERFORMANCE:`);
    console.log(`   Dataset: ${bestResult.dataFile}`);
    console.log(`   Result: ${bestResult.finalBalance.toFixed(2)} USDT (${bestResult.returnMultiplier.toFixed(2)}x)`);
    console.log(`   Target: ${bestResult.targetAchieved ? '✅ ACHIEVED' : '❌ Not reached'}`);
    console.log(`   Trades: ${bestResult.totalTrades} | Win Rate: ${bestResult.winRate.toFixed(1)}%`);

    // Save results
    fs.writeFileSync("ultra_20x_results.json", JSON.stringify({
      timestamp: new Date().toISOString(),
      summary: {
        datasetsTotal: dataFiles.length,
        successCount,
        successRate: successCount / dataFiles.length * 100
      },
      bestResult,
      allResults: results
    }, null, 2));

    console.log(`\n💾 Results saved to ultra_20x_results.json`);
    
    if (successCount > 0) {
      console.log(`\n🎉 SUCCESS! The ULTRA 20X strategy achieved the 21x target on ${successCount} dataset(s)!`);
      console.log(`🚀 The strategy CAN reach your goal under the right market conditions!`);
    } else {
      console.log(`\n📈 The strategy needs further optimization to consistently reach 21x target.`);
      console.log(`💡 Best performance was ${bestResult.returnMultiplier.toFixed(2)}x - getting closer!`);
    }
    
    console.log("=" .repeat(80));
  }
}

// Run the comprehensive test
async function main() {
  const tester = new Ultra20xTest();
  await tester.testAllDatasets();
}

main().catch(console.error);
