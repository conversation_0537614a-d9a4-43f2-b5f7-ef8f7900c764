import http from 'http';
import fs from 'fs';
import path from 'path';
import url from 'url';

export class WebServer {
  private server: http.Server;
  private port: number;
  private tradeLogPath: string;

  constructor(port: number = 3000, tradeLogPath: string) {
    this.port = port;
    this.tradeLogPath = tradeLogPath;
    this.server = this.createServer();
  }

  private createServer(): http.Server {
    return http.createServer((req, res) => {
      const parsedUrl = url.parse(req.url || '', true);
      const pathname = parsedUrl.pathname;

      // Set CORS headers
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

      try {
        if (pathname === '/') {
          this.serveDashboard(res);
        } else if (pathname === '/api/trades') {
          this.serveTrades(res);
        } else if (pathname === '/api/status') {
          this.serveStatus(res);
        } else {
          this.serve404(res);
        }
      } catch (error) {
        console.error('Web server error:', error);
        this.serve500(res, error);
      }
    });
  }

  private serveDashboard(res: http.ServerResponse): void {
    const dashboardPath = path.join(process.cwd(), 'web_dashboard.html');
    
    if (!fs.existsSync(dashboardPath)) {
      this.serve404(res);
      return;
    }

    const html = fs.readFileSync(dashboardPath, 'utf8');
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  }

  private serveTrades(res: http.ServerResponse): void {
    try {
      let trades = [];
      
      if (fs.existsSync(this.tradeLogPath)) {
        const data = fs.readFileSync(this.tradeLogPath, 'utf8');
        trades = JSON.parse(data);
      }

      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(trades));
    } catch (error) {
      console.error('Error serving trades:', error);
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify([]));
    }
  }

  private serveStatus(res: http.ServerResponse): void {
    const status = {
      online: true,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage()
    };

    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(status));
  }

  private serve404(res: http.ServerResponse): void {
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end(`
      <html>
        <head><title>404 Not Found</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1>404 - Page Not Found</h1>
          <p>The requested resource was not found on this server.</p>
          <a href="/">Go back to dashboard</a>
        </body>
      </html>
    `);
  }

  private serve500(res: http.ServerResponse, error: any): void {
    res.writeHead(500, { 'Content-Type': 'text/html' });
    res.end(`
      <html>
        <head><title>500 Internal Server Error</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <h1>500 - Internal Server Error</h1>
          <p>Something went wrong on the server.</p>
          <p style="color: #666; font-size: 0.9em;">${error.message}</p>
          <a href="/">Go back to dashboard</a>
        </body>
      </html>
    `);
  }

  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server.listen(this.port, (err?: Error) => {
        if (err) {
          reject(err);
        } else {
          console.log(`🌐 Web dashboard running at http://localhost:${this.port}`);
          console.log(`📊 Access your trading dashboard in your browser`);
          resolve();
        }
      });
    });
  }

  stop(): Promise<void> {
    return new Promise((resolve) => {
      this.server.close(() => {
        console.log('🛑 Web server stopped');
        resolve();
      });
    });
  }

  getPort(): number {
    return this.port;
  }
}
