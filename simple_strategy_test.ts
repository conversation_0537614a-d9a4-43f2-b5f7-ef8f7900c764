#!/usr/bin/env node

import csv from "csv-parser";
import fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  entryReason: string;
}

interface Trade {
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  entryTime: string;
  exitTime: string;
  size: number;
  pnl: number;
  pnlPercent: number;
  reason: string;
  entryReason: string;
  leverage: number;
  balanceBefore: number;
  balanceAfter: number;
}

class SimpleStrategyTest {
  private data: CandleData[] = [];
  private balance8x: number = 40;
  private balance20x: number = 40;
  private initialBalance: number = 40;
  private trades8x: Trade[] = [];
  private trades20x: Trade[] = [];

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`📊 Loaded ${results.length} candles from ${filePath}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateIndicators(index: number): any {
    const lookback = Math.min(100, index + 1);
    const startIndex = Math.max(0, index - lookback + 1);
    const candles = this.data.slice(startIndex, index + 1);

    const closes = candles.map((c) => c.close);

    const rsiValues = RSI.calculate({ period: 14, values: closes });
    const emaFastValues = EMA.calculate({ period: 8, values: closes });
    const emaSlowValues = EMA.calculate({ period: 21, values: closes });
    const smaValues = SMA.calculate({ period: 50, values: closes });

    return {
      rsi: rsiValues[rsiValues.length - 1] || 50,
      emaFast:
        emaFastValues[emaFastValues.length - 1] || closes[closes.length - 1],
      emaSlow:
        emaSlowValues[emaSlowValues.length - 1] || closes[closes.length - 1],
      sma: smaValues[smaValues.length - 1] || closes[closes.length - 1],
    };
  }

  private simulate8xStrategy(): any {
    console.log("\n🎯 Running 8X Strategy Simulation...");

    let balance = this.initialBalance;
    let position: Position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      entryReason: "",
    };
    let trades: Trade[] = [];
    let maxBalance = balance;
    let maxDrawdown = 0;

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const indicators = this.calculateIndicators(i);

      // Simple 8X strategy logic
      if (!position.side) {
        // Entry conditions (more realistic)
        if (indicators.rsi < 40 && candle.close > indicators.emaFast) {
          // Long entry
          const size = balance * 0.4 * 50; // 40% risk, 50x leverage
          position = {
            side: "LONG",
            size,
            entryPrice: candle.close,
            entryTime: candle.time,
            leverage: 50,
            entryReason: "RSI_OVERSOLD_LONG",
          };
          console.log(
            `📈 8X LONG entry at ${
              candle.close
            } | RSI: ${indicators.rsi.toFixed(2)}`
          );
        } else if (indicators.rsi > 60 && candle.close < indicators.emaFast) {
          // Short entry
          const size = balance * 0.4 * 50; // 40% risk, 50x leverage
          position = {
            side: "SHORT",
            size,
            entryPrice: candle.close,
            entryTime: candle.time,
            leverage: 50,
            entryReason: "RSI_OVERBOUGHT_SHORT",
          };
          console.log(
            `📉 8X SHORT entry at ${
              candle.close
            } | RSI: ${indicators.rsi.toFixed(2)}`
          );
        }
      } else {
        // Exit conditions
        const pnlPercent =
          position.side === "LONG"
            ? (candle.close - position.entryPrice) / position.entryPrice
            : (position.entryPrice - candle.close) / position.entryPrice;

        let shouldExit = false;
        let exitReason = "";

        // Take profit (30% for 8x strategy)
        if (pnlPercent >= 0.3) {
          shouldExit = true;
          exitReason = "TAKE_PROFIT";
        }
        // Stop loss (1% for 8x strategy)
        else if (pnlPercent <= -0.01) {
          shouldExit = true;
          exitReason = "STOP_LOSS";
        }
        // RSI reversal
        else if (position.side === "LONG" && indicators.rsi > 80) {
          shouldExit = true;
          exitReason = "RSI_OVERBOUGHT_EXIT";
        } else if (position.side === "SHORT" && indicators.rsi < 20) {
          shouldExit = true;
          exitReason = "RSI_OVERSOLD_EXIT";
        }

        if (shouldExit) {
          const pnl =
            position.side === "LONG"
              ? ((candle.close - position.entryPrice) * position.size) /
                position.entryPrice
              : ((position.entryPrice - candle.close) * position.size) /
                position.entryPrice;

          const balanceBefore = balance;
          balance += pnl;

          if (balance > maxBalance) maxBalance = balance;
          const currentDrawdown = (maxBalance - balance) / maxBalance;
          if (currentDrawdown > maxDrawdown) maxDrawdown = currentDrawdown;

          trades.push({
            side: position.side,
            entryPrice: position.entryPrice,
            exitPrice: candle.close,
            entryTime: position.entryTime,
            exitTime: candle.time,
            size: position.size,
            pnl,
            pnlPercent,
            reason: exitReason,
            entryReason: position.entryReason,
            leverage: position.leverage,
            balanceBefore,
            balanceAfter: balance,
          });

          position = {
            side: null,
            size: 0,
            entryPrice: 0,
            entryTime: "",
            leverage: 1,
            entryReason: "",
          };
        }
      }

      // Check if 8x target achieved
      if (balance >= this.initialBalance * 8) {
        console.log(
          `🎉 8X TARGET ACHIEVED! Balance: ${balance.toFixed(2)} USDT`
        );
        break;
      }
    }

    const winningTrades = trades.filter((t) => t.pnl > 0);
    const winRate =
      trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0;
    const returnMultiplier = balance / this.initialBalance;

    return {
      strategy: "8X Strategy",
      initialBalance: this.initialBalance,
      finalBalance: balance,
      returnMultiplier,
      totalTrades: trades.length,
      winRate,
      maxDrawdown: maxDrawdown * 100,
      targetAchieved: returnMultiplier >= 8,
      trades,
    };
  }

  private simulate20xStrategy(): any {
    console.log("\n🚀 Running 20X Strategy Simulation...");

    let balance = this.initialBalance;
    let position: Position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      entryReason: "",
    };
    let trades: Trade[] = [];
    let maxBalance = balance;
    let maxDrawdown = 0;

    for (let i = 50; i < this.data.length; i++) {
      const candle = this.data[i];
      const indicators = this.calculateIndicators(i);

      // Ultra aggressive 20X strategy logic
      if (!position.side) {
        // Entry conditions (more aggressive but realistic)
        if (indicators.rsi < 35 && candle.close > indicators.emaFast) {
          // Long entry
          const size = balance * 0.7 * 100; // 70% risk, 100x leverage
          position = {
            side: "LONG",
            size,
            entryPrice: candle.close,
            entryTime: candle.time,
            leverage: 100,
            entryReason: "EXTREME_OVERSOLD_LONG",
          };
          console.log(
            `🚀 20X LONG entry at ${
              candle.close
            } | RSI: ${indicators.rsi.toFixed(2)}`
          );
        } else if (indicators.rsi > 65 && candle.close < indicators.emaFast) {
          // Short entry
          const size = balance * 0.7 * 100; // 70% risk, 100x leverage
          position = {
            side: "SHORT",
            size,
            entryPrice: candle.close,
            entryTime: candle.time,
            leverage: 100,
            entryReason: "EXTREME_OVERBOUGHT_SHORT",
          };
          console.log(
            `💥 20X SHORT entry at ${
              candle.close
            } | RSI: ${indicators.rsi.toFixed(2)}`
          );
        }
      } else {
        // Exit conditions
        const pnlPercent =
          position.side === "LONG"
            ? (candle.close - position.entryPrice) / position.entryPrice
            : (position.entryPrice - candle.close) / position.entryPrice;

        let shouldExit = false;
        let exitReason = "";

        // Take profit (60% for 20x strategy)
        if (pnlPercent >= 0.6) {
          shouldExit = true;
          exitReason = "TAKE_PROFIT";
        }
        // Stop loss (0.3% for 20x strategy - ultra tight)
        else if (pnlPercent <= -0.003) {
          shouldExit = true;
          exitReason = "STOP_LOSS";
        }
        // RSI reversal
        else if (position.side === "LONG" && indicators.rsi > 75) {
          shouldExit = true;
          exitReason = "RSI_EXTREME_EXIT";
        } else if (position.side === "SHORT" && indicators.rsi < 25) {
          shouldExit = true;
          exitReason = "RSI_EXTREME_EXIT";
        }

        if (shouldExit) {
          const pnl =
            position.side === "LONG"
              ? ((candle.close - position.entryPrice) * position.size) /
                position.entryPrice
              : ((position.entryPrice - candle.close) * position.size) /
                position.entryPrice;

          const balanceBefore = balance;
          balance += pnl;

          if (balance > maxBalance) maxBalance = balance;
          const currentDrawdown = (maxBalance - balance) / maxBalance;
          if (currentDrawdown > maxDrawdown) maxDrawdown = currentDrawdown;

          trades.push({
            side: position.side,
            entryPrice: position.entryPrice,
            exitPrice: candle.close,
            entryTime: position.entryTime,
            exitTime: candle.time,
            size: position.size,
            pnl,
            pnlPercent,
            reason: exitReason,
            entryReason: position.entryReason,
            leverage: position.leverage,
            balanceBefore,
            balanceAfter: balance,
          });

          position = {
            side: null,
            size: 0,
            entryPrice: 0,
            entryTime: "",
            leverage: 1,
            entryReason: "",
          };
        }
      }

      // Check if 20x target achieved
      if (balance >= this.initialBalance * 21) {
        console.log(
          `🎉 20X TARGET ACHIEVED! Balance: ${balance.toFixed(2)} USDT`
        );
        break;
      }
    }

    const winningTrades = trades.filter((t) => t.pnl > 0);
    const winRate =
      trades.length > 0 ? (winningTrades.length / trades.length) * 100 : 0;
    const returnMultiplier = balance / this.initialBalance;

    return {
      strategy: "20X Strategy",
      initialBalance: this.initialBalance,
      finalBalance: balance,
      returnMultiplier,
      totalTrades: trades.length,
      winRate,
      maxDrawdown: maxDrawdown * 100,
      targetAchieved: returnMultiplier >= 21,
      trades,
    };
  }

  async runTest(): Promise<void> {
    // Get first available data file
    const dataDir = "./data";
    let dataFiles: string[] = [];

    try {
      const files = fs.readdirSync(dataDir);
      dataFiles = files
        .filter((file) => file.endsWith(".csv"))
        .map((file) => `${dataDir}/${file}`);
    } catch (error) {
      console.error("❌ Error loading data files:", error);
      return;
    }

    if (dataFiles.length === 0) {
      console.error("❌ No CSV data files found in ./data directory");
      return;
    }

    const testFile = dataFiles[0];
    console.log("🚀 ULTIMATE STRATEGY COMPARISON TEST");
    console.log("=".repeat(60));
    console.log(`📊 Testing on: ${testFile}`);
    console.log("=".repeat(60));

    await this.loadData(testFile);

    // Run both strategies
    const results8x = this.simulate8xStrategy();
    const results20x = this.simulate20xStrategy();

    // Compare and display results
    console.log("\n🏆 STRATEGY COMPARISON RESULTS");
    console.log("=".repeat(60));

    console.log(`\n📊 8X Strategy Results:`);
    console.log(
      `   Final Balance: ${results8x.finalBalance.toFixed(
        2
      )} USDT (${results8x.returnMultiplier.toFixed(2)}x)`
    );
    console.log(
      `   Target Achieved: ${results8x.targetAchieved ? "✅ YES" : "❌ NO"}`
    );
    console.log(`   Total Trades: ${results8x.totalTrades}`);
    console.log(`   Win Rate: ${results8x.winRate.toFixed(1)}%`);
    console.log(`   Max Drawdown: ${results8x.maxDrawdown.toFixed(2)}%`);

    console.log(`\n🚀 20X Strategy Results:`);
    console.log(
      `   Final Balance: ${results20x.finalBalance.toFixed(
        2
      )} USDT (${results20x.returnMultiplier.toFixed(2)}x)`
    );
    console.log(
      `   Target Achieved: ${results20x.targetAchieved ? "✅ YES" : "❌ NO"}`
    );
    console.log(`   Total Trades: ${results20x.totalTrades}`);
    console.log(`   Win Rate: ${results20x.winRate.toFixed(1)}%`);
    console.log(`   Max Drawdown: ${results20x.maxDrawdown.toFixed(2)}%`);

    // Determine winner
    let winner = "TIE";
    if (results8x.targetAchieved && !results20x.targetAchieved) {
      winner = "8X Strategy";
    } else if (results20x.targetAchieved && !results8x.targetAchieved) {
      winner = "20X Strategy";
    } else if (results8x.targetAchieved && results20x.targetAchieved) {
      winner =
        results8x.finalBalance > results20x.finalBalance
          ? "8X Strategy"
          : "20X Strategy";
    } else {
      winner =
        results8x.returnMultiplier > results20x.returnMultiplier
          ? "8X Strategy"
          : "20X Strategy";
    }

    console.log(`\n🏆 Winner: ${winner}`);

    // Save results
    const combinedResults = {
      timestamp: new Date().toISOString(),
      dataFile: testFile,
      strategy8x: results8x,
      strategy20x: results20x,
      winner,
    };

    fs.writeFileSync(
      "strategy_test_results.json",
      JSON.stringify(combinedResults, null, 2)
    );
    console.log(`\n💾 Results saved to strategy_test_results.json`);

    // Generate simple HTML report
    this.generateSimpleHTMLReport(combinedResults);

    console.log(
      "\n🎉 Test completed! Check strategy_test_results.json and strategy_report.html"
    );
  }

  private generateSimpleHTMLReport(results: any): void {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Strategy Comparison Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .strategy { display: inline-block; width: 45%; margin: 2%; padding: 20px; border-radius: 8px; vertical-align: top; }
        .strategy-8x { background: #e8f5e8; border-left: 5px solid #4CAF50; }
        .strategy-20x { background: #ffe8e8; border-left: 5px solid #FF5722; }
        .metric { margin: 10px 0; }
        .winner { text-align: center; font-size: 1.5em; margin: 20px 0; padding: 15px; background: #fff3cd; border-radius: 8px; }
        .achieved { color: #4CAF50; font-weight: bold; }
        .missed { color: #f44336; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Ultimate Trading Strategy Comparison</h1>
            <p>8X vs 20X Strategy Performance Analysis</p>
            <p><small>Generated: ${new Date(
              results.timestamp
            ).toLocaleString()}</small></p>
        </div>
        
        <div class="winner">
            🏆 Winner: <strong>${results.winner}</strong>
        </div>
        
        <div class="strategy strategy-8x">
            <h2>🎯 8X Strategy</h2>
            <div class="metric">Initial Balance: ${
              results.strategy8x.initialBalance
            } USDT</div>
            <div class="metric">Final Balance: ${results.strategy8x.finalBalance.toFixed(
              2
            )} USDT</div>
            <div class="metric">Return Multiplier: ${results.strategy8x.returnMultiplier.toFixed(
              2
            )}x</div>
            <div class="metric">Target Achieved: <span class="${
              results.strategy8x.targetAchieved ? "achieved" : "missed"
            }">${
      results.strategy8x.targetAchieved ? "✅ YES" : "❌ NO"
    }</span></div>
            <div class="metric">Total Trades: ${
              results.strategy8x.totalTrades
            }</div>
            <div class="metric">Win Rate: ${results.strategy8x.winRate.toFixed(
              1
            )}%</div>
            <div class="metric">Max Drawdown: ${results.strategy8x.maxDrawdown.toFixed(
              2
            )}%</div>
        </div>
        
        <div class="strategy strategy-20x">
            <h2>🚀 20X Strategy</h2>
            <div class="metric">Initial Balance: ${
              results.strategy20x.initialBalance
            } USDT</div>
            <div class="metric">Final Balance: ${results.strategy20x.finalBalance.toFixed(
              2
            )} USDT</div>
            <div class="metric">Return Multiplier: ${results.strategy20x.returnMultiplier.toFixed(
              2
            )}x</div>
            <div class="metric">Target Achieved: <span class="${
              results.strategy20x.targetAchieved ? "achieved" : "missed"
            }">${
      results.strategy20x.targetAchieved ? "✅ YES" : "❌ NO"
    }</span></div>
            <div class="metric">Total Trades: ${
              results.strategy20x.totalTrades
            }</div>
            <div class="metric">Win Rate: ${results.strategy20x.winRate.toFixed(
              1
            )}%</div>
            <div class="metric">Max Drawdown: ${results.strategy20x.maxDrawdown.toFixed(
              2
            )}%</div>
        </div>
        
        <div style="clear: both; text-align: center; margin-top: 30px; color: #666;">
            <p>⚠️ This is a backtest simulation. Past performance does not guarantee future results.</p>
            <p>🔥 Choose your strategy and start trading with confidence!</p>
        </div>
    </div>
</body>
</html>
    `;

    fs.writeFileSync("strategy_report.html", html);
    console.log("📄 HTML report generated: strategy_report.html");
  }
}

// Run the test
async function main() {
  const test = new SimpleStrategyTest();
  await test.runTest();
}

main().catch(console.error);
