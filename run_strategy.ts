#!/usr/bin/env node

import { Ultimate8xStrategy } from "./ultimate_8x_strategy.js";
import { Ultimate20xStrategy } from "./ultimate_20x_strategy.js";
import { StrategyComparison } from "./strategy_comparison.js";
import fs from "fs";

interface StrategyOption {
  key: string;
  name: string;
  description: string;
  target: string;
  riskLevel: string;
}

const STRATEGIES: StrategyOption[] = [
  {
    key: "8x",
    name: "Ultimate 8X Strategy",
    description: "Targets 8x returns with balanced risk-reward",
    target: "40 USDT → 320 USDT (8x)",
    riskLevel: "HIGH"
  },
  {
    key: "20x",
    name: "Ultimate 20X Strategy", 
    description: "Targets 21x returns with maximum aggression",
    target: "40 USDT → 840 USDT (21x)",
    riskLevel: "ULTRA HIGH"
  },
  {
    key: "compare",
    name: "Strategy Comparison",
    description: "Compare both strategies side by side",
    target: "Performance Analysis",
    riskLevel: "ANALYSIS"
  },
  {
    key: "all",
    name: "Full Analysis",
    description: "Test both strategies on all datasets",
    target: "Comprehensive Testing",
    riskLevel: "COMPLETE"
  }
];

class StrategyRunner {
  private dataFiles: string[] = [];

  constructor() {
    this.loadDataFiles();
  }

  private loadDataFiles(): void {
    const dataDir = "./data";
    try {
      const files = fs.readdirSync(dataDir);
      this.dataFiles = files
        .filter(file => file.endsWith('.csv'))
        .map(file => `${dataDir}/${file}`);
    } catch (error) {
      console.error("Error loading data files:", error);
      this.dataFiles = [];
    }
  }

  private printHeader(): void {
    console.log("\n" + "=".repeat(80));
    console.log("🚀 ULTIMATE TRADING STRATEGY RUNNER");
    console.log("=".repeat(80));
    console.log("Choose your trading strategy for maximum returns!");
    console.log("=".repeat(80));
  }

  private printStrategies(): void {
    console.log("\n📋 AVAILABLE STRATEGIES:\n");
    
    STRATEGIES.forEach((strategy, index) => {
      const riskColor = strategy.riskLevel === "ULTRA HIGH" ? "🔴" : 
                       strategy.riskLevel === "HIGH" ? "🟡" : 
                       strategy.riskLevel === "ANALYSIS" ? "🔵" : "🟢";
      
      console.log(`   ${index + 1}. ${strategy.name}`);
      console.log(`      ${strategy.description}`);
      console.log(`      Target: ${strategy.target}`);
      console.log(`      Risk: ${riskColor} ${strategy.riskLevel}`);
      console.log("");
    });
  }

  private printDataFiles(): void {
    if (this.dataFiles.length === 0) {
      console.log("❌ No data files found in ./data directory");
      return;
    }

    console.log("📊 AVAILABLE DATASETS:\n");
    this.dataFiles.forEach((file, index) => {
      const fileName = file.split('/').pop() || file;
      console.log(`   ${index + 1}. ${fileName}`);
    });
    console.log("");
  }

  async runStrategy(strategyKey: string, dataFileIndex?: number): Promise<void> {
    const strategy = STRATEGIES.find(s => s.key === strategyKey);
    if (!strategy) {
      console.error(`❌ Unknown strategy: ${strategyKey}`);
      return;
    }

    const dataFile = dataFileIndex !== undefined ? this.dataFiles[dataFileIndex] : this.dataFiles[0];
    if (!dataFile) {
      console.error("❌ No data file available");
      return;
    }

    console.log(`\n🚀 Running ${strategy.name}...`);
    console.log(`📊 Using dataset: ${dataFile}`);
    console.log("=".repeat(60));

    try {
      switch (strategyKey) {
        case "8x":
          await this.run8xStrategy(dataFile);
          break;
        case "20x":
          await this.run20xStrategy(dataFile);
          break;
        case "compare":
          await this.runComparison(dataFile);
          break;
        case "all":
          await this.runFullAnalysis();
          break;
        default:
          console.error(`❌ Strategy ${strategyKey} not implemented`);
      }
    } catch (error) {
      console.error(`❌ Error running strategy:`, error);
    }
  }

  private async run8xStrategy(dataFile: string): Promise<void> {
    const strategy = new Ultimate8xStrategy(40);
    await strategy.loadData(dataFile);
    await strategy.runBacktest();
    
    const results = strategy.getResults();
    this.saveResults(results, "8x_strategy_results.json");
  }

  private async run20xStrategy(dataFile: string): Promise<void> {
    const strategy = new Ultimate20xStrategy(40);
    await strategy.loadData(dataFile);
    await strategy.runBacktest();
    
    const results = strategy.getResults();
    this.saveResults(results, "20x_strategy_results.json");
  }

  private async runComparison(dataFile: string): Promise<void> {
    const comparison = new StrategyComparison();
    const result = await comparison.runComparison(dataFile);
    comparison.saveResults("strategy_comparison_single.json");
  }

  private async runFullAnalysis(): Promise<void> {
    const comparison = new StrategyComparison();
    await comparison.runAllDatasets();
    comparison.saveResults("strategy_comparison_full.json");
  }

  private saveResults(results: any, filename: string): void {
    fs.writeFileSync(filename, JSON.stringify(results, null, 2));
    console.log(`💾 Results saved to ${filename}`);
  }

  async runInteractive(): Promise<void> {
    this.printHeader();
    this.printStrategies();
    this.printDataFiles();

    // For now, we'll run a default comparison since we can't do interactive input in this environment
    console.log("🔄 Running default strategy comparison...\n");
    await this.runStrategy("compare");
  }

  async runFromArgs(): Promise<void> {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
      await this.runInteractive();
      return;
    }

    const strategyArg = args[0];
    const dataFileArg = args[1] ? parseInt(args[1]) - 1 : 0;

    // Map common argument variations
    const strategyMap: { [key: string]: string } = {
      "8": "8x",
      "8x": "8x",
      "20": "20x", 
      "20x": "20x",
      "compare": "compare",
      "comparison": "compare",
      "all": "all",
      "full": "all"
    };

    const mappedStrategy = strategyMap[strategyArg.toLowerCase()];
    if (!mappedStrategy) {
      console.error(`❌ Unknown strategy: ${strategyArg}`);
      console.log("Available strategies: 8x, 20x, compare, all");
      return;
    }

    await this.runStrategy(mappedStrategy, dataFileArg);
  }
}

// Main execution
async function main() {
  const runner = new StrategyRunner();
  
  console.log("🚀 ULTIMATE TRADING STRATEGY SYSTEM");
  console.log("Choose between 8X and 20X strategies for maximum returns!");
  
  try {
    await runner.runFromArgs();
  } catch (error) {
    console.error("❌ Fatal error:", error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { StrategyRunner };
