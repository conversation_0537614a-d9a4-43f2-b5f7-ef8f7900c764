import csv from "csv-parser";
import fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

// Types for our trading system
interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface Position {
  side: "LONG" | "SHORT" | null;
  size: number;
  entryPrice: number;
  entryTime: string;
  leverage: number;
  highestPrice?: number; // For trailing stop
  lowestPrice?: number; // For trailing stop
}

interface Trade {
  entryTime: string;
  exitTime: string;
  side: "LONG" | "SHORT";
  entryPrice: number;
  exitPrice: number;
  size: number;
  pnl: number;
  pnlPercent: number;
  leverage: number;
}

interface BacktestResult {
  initialBalance: number;
  finalBalance: number;
  totalReturn: number;
  totalReturnPercent: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Trade[];
}

class FuturesBacktester {
  private data: CandleData[] = [];
  private balance: number;
  private initialBalance: number;
  private position: Position = {
    side: null,
    size: 0,
    entryPrice: 0,
    entryTime: "",
    leverage: 1,
  };
  private trades: Trade[] = [];
  private maxBalance: number;
  private maxDrawdown: number = 0;

  // Strategy parameters - DIVERGENCE HUNTER for 8x target
  private readonly RSI_PERIOD = 14;
  private readonly EMA_FAST = 8;
  private readonly EMA_SLOW = 21;
  private readonly SMA_PERIOD = 50;
  private readonly MACD_FAST = 12;
  private readonly MACD_SLOW = 26;
  private readonly MACD_SIGNAL = 9;
  private readonly RSI_OVERSOLD = 15; // Ultra extreme oversold
  private readonly RSI_OVERBOUGHT = 85; // Ultra extreme overbought
  private readonly LEVERAGE = 35; // MAXIMUM leverage for explosive gains
  private readonly RISK_PER_TRADE = 0.3; // 30% risk per trade for maximum growth
  private readonly STOP_LOSS_PERCENT = 0.01; // 1% ultra tight stop loss
  private readonly TAKE_PROFIT_PERCENT = 0.2; // 20% take profit (20:1 R:R)
  private readonly DIVERGENCE_LOOKBACK = 10; // Periods to look back for divergence

  constructor(initialBalance: number = 100) {
    this.balance = initialBalance;
    this.initialBalance = initialBalance;
    this.maxBalance = initialBalance;
  }

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`Loaded ${results.length} candles from ${filePath}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateIndicators(
    index: number
  ): {
    rsi: number;
    emaFast: number;
    emaSlow: number;
    sma: number;
    macdLine: number;
    macdSignal: number;
    macdHistogram: number;
  } | null {
    if (
      index <
      Math.max(
        this.RSI_PERIOD,
        this.EMA_SLOW,
        this.SMA_PERIOD,
        this.MACD_SLOW + this.MACD_SIGNAL
      )
    ) {
      return null;
    }

    const closes = this.data.slice(0, index + 1).map((d) => d.close);

    const rsiValues = RSI.calculate({
      values: closes,
      period: this.RSI_PERIOD,
    });
    const emaFastValues = EMA.calculate({
      values: closes,
      period: this.EMA_FAST,
    });
    const emaSlowValues = EMA.calculate({
      values: closes,
      period: this.EMA_SLOW,
    });
    const smaValues = SMA.calculate({
      values: closes,
      period: this.SMA_PERIOD,
    });

    // Calculate MACD manually
    const ema12 = EMA.calculate({ values: closes, period: this.MACD_FAST });
    const ema26 = EMA.calculate({ values: closes, period: this.MACD_SLOW });

    const macdLine = ema12[ema12.length - 1] - ema26[ema26.length - 1];

    // Calculate MACD signal line (EMA of MACD line)
    const macdHistory = [];
    for (let i = this.MACD_SLOW - 1; i < closes.length; i++) {
      const ema12Val = EMA.calculate({
        values: closes.slice(0, i + 1),
        period: this.MACD_FAST,
      });
      const ema26Val = EMA.calculate({
        values: closes.slice(0, i + 1),
        period: this.MACD_SLOW,
      });
      macdHistory.push(
        ema12Val[ema12Val.length - 1] - ema26Val[ema26Val.length - 1]
      );
    }

    const macdSignalValues = EMA.calculate({
      values: macdHistory,
      period: this.MACD_SIGNAL,
    });
    const macdSignal = macdSignalValues[macdSignalValues.length - 1] || 0;
    const macdHistogram = macdLine - macdSignal;

    return {
      rsi: rsiValues[rsiValues.length - 1] || 0,
      emaFast: emaFastValues[emaFastValues.length - 1] || 0,
      emaSlow: emaSlowValues[emaSlowValues.length - 1] || 0,
      sma: smaValues[smaValues.length - 1] || 0,
      macdLine,
      macdSignal,
      macdHistogram,
    };
  }

  // Advanced divergence detection methods
  private detectBullishRSIDivergence(index: number): boolean {
    if (index < this.DIVERGENCE_LOOKBACK + this.RSI_PERIOD) return false;

    const currentPrice = this.data[index].close;
    const currentRSI = this.calculateIndicators(index)?.rsi || 0;

    // Look for lower lows in price but higher lows in RSI
    for (let i = 1; i <= this.DIVERGENCE_LOOKBACK; i++) {
      const pastIndex = index - i;
      const pastPrice = this.data[pastIndex].close;
      const pastRSI = this.calculateIndicators(pastIndex)?.rsi || 0;

      if (currentPrice < pastPrice && currentRSI > pastRSI && currentRSI < 40) {
        return true;
      }
    }
    return false;
  }

  private detectBearishRSIDivergence(index: number): boolean {
    if (index < this.DIVERGENCE_LOOKBACK + this.RSI_PERIOD) return false;

    const currentPrice = this.data[index].close;
    const currentRSI = this.calculateIndicators(index)?.rsi || 0;

    // Look for higher highs in price but lower highs in RSI
    for (let i = 1; i <= this.DIVERGENCE_LOOKBACK; i++) {
      const pastIndex = index - i;
      const pastPrice = this.data[pastIndex].close;
      const pastRSI = this.calculateIndicators(pastIndex)?.rsi || 0;

      if (currentPrice > pastPrice && currentRSI < pastRSI && currentRSI > 60) {
        return true;
      }
    }
    return false;
  }

  private detectBullishMACDDivergence(index: number): boolean {
    if (index < this.DIVERGENCE_LOOKBACK + this.MACD_SLOW + this.MACD_SIGNAL)
      return false;

    const currentPrice = this.data[index].close;
    const currentMACD = this.calculateIndicators(index)?.macdLine || 0;

    // Look for lower lows in price but higher lows in MACD
    for (let i = 1; i <= this.DIVERGENCE_LOOKBACK; i++) {
      const pastIndex = index - i;
      const pastPrice = this.data[pastIndex].close;
      const pastMACD = this.calculateIndicators(pastIndex)?.macdLine || 0;

      if (
        currentPrice < pastPrice &&
        currentMACD > pastMACD &&
        currentMACD < 0
      ) {
        return true;
      }
    }
    return false;
  }

  private detectBearishMACDDivergence(index: number): boolean {
    if (index < this.DIVERGENCE_LOOKBACK + this.MACD_SLOW + this.MACD_SIGNAL)
      return false;

    const currentPrice = this.data[index].close;
    const currentMACD = this.calculateIndicators(index)?.macdLine || 0;

    // Look for higher highs in price but lower highs in MACD
    for (let i = 1; i <= this.DIVERGENCE_LOOKBACK; i++) {
      const pastIndex = index - i;
      const pastPrice = this.data[pastIndex].close;
      const pastMACD = this.calculateIndicators(pastIndex)?.macdLine || 0;

      if (
        currentPrice > pastPrice &&
        currentMACD < pastMACD &&
        currentMACD > 0
      ) {
        return true;
      }
    }
    return false;
  }

  private shouldEnterLong(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData,
    index: number
  ): boolean {
    // ADVANCED DIVERGENCE & PATTERN RECOGNITION STRATEGY

    // 1. DIVERGENCE SIGNALS (Highest Priority)
    const bullishRSIDivergence = this.detectBullishRSIDivergence(index);
    const bullishMACDDivergence = this.detectBullishMACDDivergence(index);

    // 2. EXTREME OVERSOLD CONDITIONS
    const extremeOversold = indicators.rsi < this.RSI_OVERSOLD;
    const rsiRecovering = indicators.rsi > 10 && indicators.rsi < 30; // RSI recovering from extreme

    // 3. MACD SIGNALS
    const macdBullishCrossover =
      indicators.macdLine > indicators.macdSignal &&
      indicators.macdHistogram > 0;
    const macdTurningUp =
      indicators.macdHistogram > 0 && indicators.macdLine < 0; // MACD turning positive from negative

    // 4. MOMENTUM & VOLUME PATTERNS
    const explosiveGreenCandle =
      candle.close > candle.open &&
      (candle.close - candle.open) / candle.open > 0.005; // 0.5% explosive candle
    const massiveVolume = candle.volume > prevCandle.volume * 3.0; // Massive volume spike
    const strongMomentum = candle.close > prevCandle.close * 1.004; // 0.4% strong momentum

    // 5. TREND ALIGNMENT
    const bullishTrend = indicators.emaFast > indicators.emaSlow;
    const strongBullishTrend =
      indicators.emaFast > indicators.emaSlow &&
      indicators.emaSlow > indicators.sma;
    const breakoutAboveSMA =
      candle.close > indicators.sma && prevCandle.close <= indicators.sma;

    // 6. CUSTOM PATTERNS
    const hammerPattern =
      candle.close - candle.low > 2 * (candle.open - candle.close) &&
      candle.close > candle.open;
    const dojiBullish =
      Math.abs(candle.close - candle.open) / (candle.high - candle.low) < 0.1 &&
      indicators.rsi < 30;

    // ENTRY LOGIC - Multiple high-probability scenarios
    return (
      // TIER 1: DIVERGENCE SIGNALS (Highest probability)
      (bullishRSIDivergence && explosiveGreenCandle && massiveVolume) ||
      (bullishMACDDivergence && macdBullishCrossover && strongMomentum) ||
      // TIER 2: EXTREME CONDITIONS
      (extremeOversold &&
        explosiveGreenCandle &&
        massiveVolume &&
        bullishTrend) ||
      (macdTurningUp && rsiRecovering && strongMomentum && massiveVolume) ||
      // TIER 3: BREAKOUT PATTERNS
      (breakoutAboveSMA &&
        strongMomentum &&
        massiveVolume &&
        indicators.rsi < 40) ||
      (strongBullishTrend &&
        explosiveGreenCandle &&
        massiveVolume &&
        indicators.rsi < 50) ||
      // TIER 4: CUSTOM PATTERNS
      (hammerPattern && indicators.rsi < 25 && massiveVolume) ||
      (dojiBullish && bullishTrend && strongMomentum)
    );
  }

  private shouldEnterShort(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData,
    index: number
  ): boolean {
    // ADVANCED DIVERGENCE & PATTERN RECOGNITION STRATEGY

    // 1. DIVERGENCE SIGNALS (Highest Priority)
    const bearishRSIDivergence = this.detectBearishRSIDivergence(index);
    const bearishMACDDivergence = this.detectBearishMACDDivergence(index);

    // 2. EXTREME OVERBOUGHT CONDITIONS
    const extremeOverbought = indicators.rsi > this.RSI_OVERBOUGHT;
    const rsiWeakening = indicators.rsi < 90 && indicators.rsi > 70; // RSI weakening from extreme

    // 3. MACD SIGNALS
    const macdBearishCrossover =
      indicators.macdLine < indicators.macdSignal &&
      indicators.macdHistogram < 0;
    const macdTurningDown =
      indicators.macdHistogram < 0 && indicators.macdLine > 0; // MACD turning negative from positive

    // 4. MOMENTUM & VOLUME PATTERNS
    const explosiveRedCandle =
      candle.close < candle.open &&
      (candle.open - candle.close) / candle.open > 0.005; // 0.5% explosive candle
    const massiveVolume = candle.volume > prevCandle.volume * 3.0; // Massive volume spike
    const strongMomentum = candle.close < prevCandle.close * 0.996; // 0.4% strong downward momentum

    // 5. TREND ALIGNMENT
    const bearishTrend = indicators.emaFast < indicators.emaSlow;
    const strongBearishTrend =
      indicators.emaFast < indicators.emaSlow &&
      indicators.emaSlow < indicators.sma;
    const breakdownBelowSMA =
      candle.close < indicators.sma && prevCandle.close >= indicators.sma;

    // 6. CUSTOM PATTERNS
    const shootingStarPattern =
      candle.high - candle.close > 2 * (candle.close - candle.open) &&
      candle.close < candle.open;
    const dojiBearish =
      Math.abs(candle.close - candle.open) / (candle.high - candle.low) < 0.1 &&
      indicators.rsi > 70;

    // ENTRY LOGIC - Multiple high-probability scenarios
    return (
      // TIER 1: DIVERGENCE SIGNALS (Highest probability)
      (bearishRSIDivergence && explosiveRedCandle && massiveVolume) ||
      (bearishMACDDivergence && macdBearishCrossover && strongMomentum) ||
      // TIER 2: EXTREME CONDITIONS
      (extremeOverbought &&
        explosiveRedCandle &&
        massiveVolume &&
        bearishTrend) ||
      (macdTurningDown && rsiWeakening && strongMomentum && massiveVolume) ||
      // TIER 3: BREAKDOWN PATTERNS
      (breakdownBelowSMA &&
        strongMomentum &&
        massiveVolume &&
        indicators.rsi > 60) ||
      (strongBearishTrend &&
        explosiveRedCandle &&
        massiveVolume &&
        indicators.rsi > 50) ||
      // TIER 4: CUSTOM PATTERNS
      (shootingStarPattern && indicators.rsi > 75 && massiveVolume) ||
      (dojiBearish && bearishTrend && strongMomentum)
    );
  }

  private shouldExitPosition(candle: CandleData, indicators: any): boolean {
    if (!this.position.side) return false;

    const currentPrice = candle.close;
    const entryPrice = this.position.entryPrice;

    // Update highest/lowest prices for trailing stop
    if (this.position.side === "LONG") {
      if (
        !this.position.highestPrice ||
        currentPrice > this.position.highestPrice
      ) {
        this.position.highestPrice = currentPrice;
      }
    } else {
      if (
        !this.position.lowestPrice ||
        currentPrice < this.position.lowestPrice
      ) {
        this.position.lowestPrice = currentPrice;
      }
    }

    // Calculate PnL percentage
    const pnlPercent =
      this.position.side === "LONG"
        ? (currentPrice - entryPrice) / entryPrice
        : (entryPrice - currentPrice) / entryPrice;

    // Hard stop loss
    if (pnlPercent <= -this.STOP_LOSS_PERCENT) {
      return true;
    }

    // Take profit at target
    if (pnlPercent >= this.TAKE_PROFIT_PERCENT) {
      return true;
    }

    // Trailing stop logic - activate after 3% profit
    if (pnlPercent > 0.03) {
      const trailingStopPercent = 0.04; // 4% trailing stop

      if (this.position.side === "LONG" && this.position.highestPrice) {
        const trailingStopPrice =
          this.position.highestPrice * (1 - trailingStopPercent);
        if (currentPrice <= trailingStopPrice) {
          return true;
        }
      }

      if (this.position.side === "SHORT" && this.position.lowestPrice) {
        const trailingStopPrice =
          this.position.lowestPrice * (1 + trailingStopPercent);
        if (currentPrice >= trailingStopPrice) {
          return true;
        }
      }
    }

    // RSI extreme reversal signals (stronger than before)
    if (this.position.side === "LONG" && indicators.rsi > 75) {
      return true;
    }

    if (this.position.side === "SHORT" && indicators.rsi < 25) {
      return true;
    }

    // EMA crossover reversal (only if not profitable)
    if (pnlPercent < 0.02) {
      if (
        this.position.side === "LONG" &&
        indicators.emaFast < indicators.emaSlow
      ) {
        return true;
      }

      if (
        this.position.side === "SHORT" &&
        indicators.emaFast > indicators.emaSlow
      ) {
        return true;
      }
    }

    return false;
  }

  private calculatePositionSize(): number {
    // DIVERGENCE HUNTER - ULTRA AGGRESSIVE compound growth for 8x target
    let riskPercent = this.RISK_PER_TRADE;

    // Extreme dynamic risk scaling for maximum growth
    if (this.balance > this.initialBalance * 7) {
      riskPercent = 0.25; // Still very aggressive when 7x ahead
    } else if (this.balance > this.initialBalance * 5) {
      riskPercent = 0.28; // Aggressive when 5x ahead
    } else if (this.balance > this.initialBalance * 3) {
      riskPercent = 0.3; // Very aggressive when 3x ahead
    } else if (this.balance > this.initialBalance * 2) {
      riskPercent = 0.32; // Ultra aggressive when 2x ahead
    } else if (this.balance > this.initialBalance * 1.5) {
      riskPercent = 0.35; // Maximum aggression when 1.5x ahead
    } else {
      riskPercent = 0.4; // INSANE aggression when behind - go big or go home
    }

    const currentRiskAmount = this.balance * riskPercent;
    const maxRiskAmount = this.balance * 0.6; // Max 60% of current balance

    // Use current balance for explosive compound growth
    const riskAmount = Math.min(currentRiskAmount, maxRiskAmount);
    return riskAmount * this.LEVERAGE;
  }

  private openPosition(
    side: "LONG" | "SHORT",
    price: number,
    time: string
  ): void {
    const size = this.calculatePositionSize();

    this.position = {
      side,
      size,
      entryPrice: price,
      entryTime: time,
      leverage: this.LEVERAGE,
      highestPrice: side === "LONG" ? price : undefined,
      lowestPrice: side === "SHORT" ? price : undefined,
    };

    console.log(
      `Opened ${side} position at ${price} with size ${size.toFixed(
        4
      )} (${time})`
    );
  }

  private closePosition(price: number, time: string): void {
    if (!this.position.side) return;

    // Calculate price change percentage
    const priceChangePercent =
      this.position.side === "LONG"
        ? (price - this.position.entryPrice) / this.position.entryPrice
        : (this.position.entryPrice - price) / this.position.entryPrice;

    // Calculate PnL as percentage of position value with leverage
    const positionValue = this.position.size / this.LEVERAGE;
    const pnl = positionValue * priceChangePercent * this.LEVERAGE;
    const pnlPercent = priceChangePercent * this.LEVERAGE * 100;

    // Cap maximum loss to prevent account blowup
    const maxLoss = this.balance * 0.1; // Max 10% loss per trade
    const cappedPnl = Math.max(pnl, -maxLoss);

    this.balance += cappedPnl;

    // Track max balance and drawdown
    if (this.balance > this.maxBalance) {
      this.maxBalance = this.balance;
    }

    const currentDrawdown = (this.maxBalance - this.balance) / this.maxBalance;
    if (currentDrawdown > this.maxDrawdown) {
      this.maxDrawdown = currentDrawdown;
    }

    const trade: Trade = {
      entryTime: this.position.entryTime,
      exitTime: time,
      side: this.position.side,
      entryPrice: this.position.entryPrice,
      exitPrice: price,
      size: this.position.size,
      pnl: cappedPnl,
      pnlPercent,
      leverage: this.position.leverage,
    };

    this.trades.push(trade);

    console.log(
      `Closed ${
        this.position.side
      } position at ${price}. PnL: ${cappedPnl.toFixed(
        2
      )} USDT (${pnlPercent.toFixed(2)}%). Balance: ${this.balance.toFixed(
        2
      )} USDT`
    );

    // Reset position
    this.position = {
      side: null,
      size: 0,
      entryPrice: 0,
      entryTime: "",
      leverage: 1,
      highestPrice: undefined,
      lowestPrice: undefined,
    };
  }

  async runBacktest(): Promise<BacktestResult> {
    console.log("Starting backtest...");
    console.log(`Initial balance: ${this.initialBalance} USDT`);
    console.log(`Data points: ${this.data.length}`);

    for (let i = 1; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      if (!indicators) continue;

      // Check for exit signals first
      if (this.position.side && this.shouldExitPosition(candle, indicators)) {
        this.closePosition(candle.close, candle.time);
      }

      // Check for entry signals if no position
      if (!this.position.side) {
        if (this.shouldEnterLong(candle, indicators, prevCandle, i)) {
          this.openPosition("LONG", candle.close, candle.time);
        } else if (this.shouldEnterShort(candle, indicators, prevCandle, i)) {
          this.openPosition("SHORT", candle.close, candle.time);
        }
      }
    }

    // Close any remaining position
    if (this.position.side) {
      const lastCandle = this.data[this.data.length - 1];
      this.closePosition(lastCandle.close, lastCandle.time);
    }

    return this.generateResults();
  }

  private generateResults(): BacktestResult {
    const totalReturn = this.balance - this.initialBalance;
    const totalReturnPercent = (totalReturn / this.initialBalance) * 100;
    const winningTrades = this.trades.filter((t) => t.pnl > 0).length;
    const losingTrades = this.trades.filter((t) => t.pnl <= 0).length;
    const winRate =
      this.trades.length > 0 ? (winningTrades / this.trades.length) * 100 : 0;

    // Calculate Sharpe ratio (simplified)
    const returns = this.trades.map((t) => t.pnlPercent);
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length || 0;
    const returnStdDev =
      Math.sqrt(
        returns.reduce((a, b) => a + Math.pow(b - avgReturn, 2), 0) /
          returns.length
      ) || 1;
    const sharpeRatio = avgReturn / returnStdDev;

    return {
      initialBalance: this.initialBalance,
      finalBalance: this.balance,
      totalReturn,
      totalReturnPercent,
      totalTrades: this.trades.length,
      winningTrades,
      losingTrades,
      winRate,
      maxDrawdown: this.maxDrawdown * 100,
      sharpeRatio,
      trades: this.trades,
    };
  }
}

// Main execution
async function main() {
  const backtester = new FuturesBacktester(100); // 100 USDT initial balance

  try {
    await backtester.loadData("./data/historical_data_mar_15m.csv");
    const results = await backtester.runBacktest();

    console.log("\n=== BACKTEST RESULTS ===");
    console.log(`Initial Balance: ${results.initialBalance.toFixed(2)} USDT`);
    console.log(`Final Balance: ${results.finalBalance.toFixed(2)} USDT`);
    console.log(
      `Total Return: ${results.totalReturn.toFixed(
        2
      )} USDT (${results.totalReturnPercent.toFixed(2)}%)`
    );
    console.log(`Total Trades: ${results.totalTrades}`);
    console.log(`Winning Trades: ${results.winningTrades}`);
    console.log(`Losing Trades: ${results.losingTrades}`);
    console.log(`Win Rate: ${results.winRate.toFixed(2)}%`);
    console.log(`Max Drawdown: ${results.maxDrawdown.toFixed(2)}%`);
    console.log(`Sharpe Ratio: ${results.sharpeRatio.toFixed(2)}`);

    if (results.finalBalance >= 800) {
      console.log("\n🎉 SUCCESS! Target of 800 USDT achieved!");
    } else {
      console.log(
        `\n📊 Target not reached. Need ${(800 - results.finalBalance).toFixed(
          2
        )} more USDT to reach 800 USDT target.`
      );
    }

    // Show last 10 trades
    console.log("\n=== LAST 10 TRADES ===");
    const lastTrades = results.trades.slice(-10);
    lastTrades.forEach((trade, index) => {
      console.log(
        `${index + 1}. ${trade.side} ${trade.entryPrice.toFixed(
          2
        )} -> ${trade.exitPrice.toFixed(2)} | PnL: ${trade.pnl.toFixed(
          2
        )} USDT (${trade.pnlPercent.toFixed(2)}%)`
      );
    });
  } catch (error) {
    console.error("Error running backtest:", error);
  }
}

// Run the backtest
main();
