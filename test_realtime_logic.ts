import csv from "csv-parser";
import fs from "fs";
import { EMA, RSI, SMA } from "technicalindicators";

// Types for our trading system
interface CandleData {
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface MarketState {
  volatility: number;
  momentum: number;
  volume: number;
  trend: number;
  regime: "EXPLOSIVE" | "TRENDING" | "CHOPPY" | "DEAD";
}

class RealTimeLogicTest {
  private data: CandleData[] = [];
  private marketState: MarketState = {
    volatility: 0,
    momentum: 0,
    volume: 0,
    trend: 0,
    regime: "CHOPPY",
  };

  // Strategy parameters (match real-time bot)
  private readonly RSI_PERIOD = 14;
  private readonly EMA_FAST = 8;
  private readonly EMA_SLOW = 21;
  private readonly SMA_PERIOD = 50;
  private readonly VOLATILITY_PERIOD = 20;

  // Dynamic parameters for DEAD market
  private RSI_OVERSOLD = 25;
  private RSI_OVERBOUGHT = 75;
  private MOMENTUM_THRESHOLD = 0.004;
  private VOLUME_MULTIPLIER = 4.0;

  async loadData(filePath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const results: CandleData[] = [];

      fs.createReadStream(filePath)
        .pipe(csv())
        .on("data", (data) => {
          results.push({
            time: data.time,
            open: parseFloat(data.open),
            high: parseFloat(data.high),
            low: parseFloat(data.low),
            close: parseFloat(data.close),
            volume: parseFloat(data.volume),
          });
        })
        .on("end", () => {
          this.data = results;
          console.log(`Loaded ${results.length} candles from ${filePath}`);
          resolve();
        })
        .on("error", reject);
    });
  }

  private calculateMarketState(index: number): MarketState {
    if (index < this.VOLATILITY_PERIOD + this.SMA_PERIOD) {
      return this.marketState;
    }

    const recentCandles = this.data.slice(
      Math.max(0, index - this.VOLATILITY_PERIOD),
      index + 1
    );
    const closes = recentCandles.map((c) => c.close);
    const volumes = recentCandles.map((c) => c.volume);
    const highs = recentCandles.map((c) => c.high);
    const lows = recentCandles.map((c) => c.low);

    // Calculate volatility (ATR-based)
    let atrSum = 0;
    for (let i = 1; i < recentCandles.length; i++) {
      const tr = Math.max(
        highs[i] - lows[i],
        Math.abs(highs[i] - closes[i - 1]),
        Math.abs(lows[i] - closes[i - 1])
      );
      atrSum += tr;
    }
    const volatility =
      atrSum / (recentCandles.length - 1) / closes[closes.length - 1];

    // Calculate momentum
    const momentum = Math.abs(
      (closes[closes.length - 1] - closes[0]) / closes[0]
    );

    // Calculate volume ratio (match real-time bot logic)
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
    const recentVolume = volumes.slice(-5).reduce((a, b) => a + b, 0) / 5;
    const volumeRatio = recentVolume / avgVolume;

    // Calculate trend
    const smaValues = SMA.calculate({
      values: closes,
      period: Math.min(10, closes.length),
    });
    const currentSMA = smaValues[smaValues.length - 1];
    const pastSMA = smaValues[Math.max(0, smaValues.length - 5)];
    const trend = Math.abs((currentSMA - pastSMA) / pastSMA);

    // Determine market regime
    let regime: "EXPLOSIVE" | "TRENDING" | "CHOPPY" | "DEAD";

    if (volatility > 0.03 && volumeRatio > 2.0 && momentum > 0.02) {
      regime = "EXPLOSIVE";
    } else if (volatility > 0.015 && volumeRatio > 1.3 && trend > 0.005) {
      regime = "TRENDING";
    } else if (volatility > 0.008 && volumeRatio > 0.8) {
      regime = "CHOPPY";
    } else {
      regime = "DEAD";
    }

    return {
      volatility,
      momentum,
      volume: volumeRatio,
      trend,
      regime,
    };
  }

  private calculateIndicators(index: number): any {
    if (index < Math.max(this.RSI_PERIOD, this.EMA_SLOW, this.SMA_PERIOD)) {
      return null;
    }

    const closes = this.data.slice(0, index + 1).map((d) => d.close);

    try {
      const rsiValues = RSI.calculate({
        values: closes,
        period: this.RSI_PERIOD,
      });
      const emaFastValues = EMA.calculate({
        values: closes,
        period: this.EMA_FAST,
      });
      const emaSlowValues = EMA.calculate({
        values: closes,
        period: this.EMA_SLOW,
      });
      const smaValues = SMA.calculate({
        values: closes,
        period: this.SMA_PERIOD,
      });

      return {
        rsi: rsiValues[rsiValues.length - 1] || 0,
        emaFast: emaFastValues[emaFastValues.length - 1] || 0,
        emaSlow: emaSlowValues[emaSlowValues.length - 1] || 0,
        sma: smaValues[smaValues.length - 1] || 0,
        prevRsi: rsiValues[rsiValues.length - 2] || 0,
        prevEmaFast: emaFastValues[emaFastValues.length - 2] || 0,
        prevEmaSlow: emaSlowValues[emaSlowValues.length - 2] || 0,
      };
    } catch (error) {
      return null;
    }
  }

  private shouldEnterShort(
    candle: CandleData,
    indicators: any,
    prevCandle: CandleData
  ): { enter: boolean; reason: string } {
    if (!prevCandle) return { enter: false, reason: "" };

    // DEAD market parameters
    this.RSI_OVERSOLD = 25;
    this.RSI_OVERBOUGHT = 75;
    this.MOMENTUM_THRESHOLD = 0.004;
    this.VOLUME_MULTIPLIER = 4.0;

    const rsiWeakening =
      indicators.rsi < this.RSI_OVERBOUGHT &&
      indicators.rsi > this.RSI_OVERBOUGHT - 20;

    // Momentum patterns
    const explosiveRedCandle =
      candle.close < candle.open &&
      (candle.open - candle.close) / candle.open > this.MOMENTUM_THRESHOLD;

    // Use candle-to-candle volume comparison (match backtest logic)
    const ultraVolume =
      candle.volume > prevCandle.volume * this.VOLUME_MULTIPLIER;
    const strongMomentum = this.marketState.momentum > this.MOMENTUM_THRESHOLD;

    // Trend conditions
    const bearishTrend = indicators.emaFast < indicators.emaSlow;
    const breakdownBelowSMA =
      candle.close < indicators.sma && prevCandle.close >= indicators.sma;
    const bearishEngulfing =
      candle.close < prevCandle.low && candle.open > prevCandle.close;

    // DEAD market logic (match updated real-time bot)
    if (this.marketState.regime === "DEAD") {
      if (explosiveRedCandle && ultraVolume) {
        return { enter: true, reason: "DEAD_MARKET_MOMENTUM_SHORT" };
      }
      if (rsiWeakening && strongMomentum && ultraVolume) {
        return { enter: true, reason: "DEAD_MARKET_DECLINE" };
      }
      if (breakdownBelowSMA && strongMomentum && ultraVolume) {
        return { enter: true, reason: "DEAD_MARKET_BREAKDOWN" };
      }
      if (bearishEngulfing && ultraVolume) {
        return { enter: true, reason: "DEAD_MARKET_ENGULFING" };
      }
    }

    return { enter: false, reason: "NO_SIGNAL" };
  }

  async testRealTimeLogic(): Promise<void> {
    console.log("🔍 Testing Real-Time Bot Logic on Historical Data...");
    console.log(`Data points: ${this.data.length}`);

    let signalsFound = 0;
    let lastRegime = "";

    for (let i = 1; i < this.data.length; i++) {
      const candle = this.data[i];
      const prevCandle = this.data[i - 1];
      const indicators = this.calculateIndicators(i);

      if (!indicators) continue;

      // Calculate market state
      this.marketState = this.calculateMarketState(i);

      // Log regime changes
      if (this.marketState.regime !== lastRegime) {
        console.log(
          `📊 Market regime: ${this.marketState.regime} at ${candle.time}`
        );
        lastRegime = this.marketState.regime;
      }

      // Check for short signals
      const shortSignal = this.shouldEnterShort(candle, indicators, prevCandle);

      // Debug specific times from backtest
      if (
        candle.time === "2025-06-16T22:30:00.000Z" ||
        candle.time === "2025-06-17T16:45:00.000Z"
      ) {
        console.log(`\n🔍 DEBUG at ${candle.time}:`);
        console.log(`  Market regime: ${this.marketState.regime}`);
        console.log(`  Price: ${candle.close} (prev: ${prevCandle.close})`);
        console.log(`  RSI: ${indicators.rsi.toFixed(1)}`);
        console.log(`  Volume ratio: ${this.marketState.volume.toFixed(2)}`);
        console.log(`  Momentum: ${this.marketState.momentum.toFixed(4)}`);
        console.log(
          `  Explosive red candle: ${candle.close < candle.open &&
            (candle.open - candle.close) / candle.open >
              this.MOMENTUM_THRESHOLD}`
        );
        console.log(
          `  Ultra volume: ${candle.volume >
            prevCandle.volume * this.VOLUME_MULTIPLIER} (${candle.volume} > ${(
            prevCandle.volume * this.VOLUME_MULTIPLIER
          ).toFixed(0)})`
        );
        console.log(
          `  Strong momentum: ${this.marketState.momentum >
            this.MOMENTUM_THRESHOLD}`
        );
        console.log(`  Signal result: ${shortSignal.reason}`);
      }

      if (shortSignal.enter) {
        signalsFound++;
        console.log(
          `🔴 SHORT SIGNAL #${signalsFound}: ${shortSignal.reason} at ${candle.time}`
        );
        console.log(
          `   Price: ${candle.close} | RSI: ${indicators.rsi.toFixed(1)} | ` +
            `Volume Ratio: ${this.marketState.volume.toFixed(2)} | ` +
            `Momentum: ${this.marketState.momentum.toFixed(4)}`
        );
      }
    }

    console.log(`\n📊 Summary:`);
    console.log(`Total signals found: ${signalsFound}`);
    console.log(
      `Expected signals from backtest: 2 (DEAD_MARKET_BREAKDOWN, DEAD_MARKET_MOMENTUM_SHORT)`
    );

    if (signalsFound >= 2) {
      console.log("✅ Real-time logic should now detect the same trades!");
    } else {
      console.log("❌ Real-time logic still missing some signals.");
    }
  }
}

// Main execution
async function main() {
  const tester = new RealTimeLogicTest();

  try {
    await tester.loadData("./data/historical_data_15-18-06-2025_15m.csv");
    await tester.testRealTimeLogic();
  } catch (error) {
    console.error("Error testing real-time logic:", error);
  }
}

// Run the test
main();
